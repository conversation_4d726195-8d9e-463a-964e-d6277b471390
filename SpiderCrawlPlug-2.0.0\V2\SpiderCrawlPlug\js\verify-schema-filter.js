// 抖音小店Schema API过滤器验证脚本
// 专门用于验证 https://bscm.jinritemai.com/fxg/product/tproduct/getSchema 的过滤功能

console.log('🔍 抖音小店Schema API过滤器验证脚本已加载');

// 验证函数
function verifySchemaFilter() {
    console.log('🧪 开始验证抖音小店Schema API过滤器...');
    
    // 测试URL列表
    const testCases = [
        {
            url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
            shouldMatch: true,
            description: '精确匹配 - 基础URL'
        },
        {
            url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123',
            shouldMatch: true,
            description: '精确匹配 - 带category_id参数'
        },
        {
            url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123&brand_id=456',
            shouldMatch: true,
            description: '精确匹配 - 带多个参数'
        },
        {
            url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123&brand_id=456&other=789',
            shouldMatch: true,
            description: '精确匹配 - 带更多参数'
        },
        {
            url: 'https://bscm.jinritemai.com/other/getSchema',
            shouldMatch: true,
            description: '模式匹配 - 其他路径的getSchema'
        },
        {
            url: 'https://bscm.jinritemai.com/api/tproduct/info',
            shouldMatch: true,
            description: '关键词匹配 - 包含tproduct'
        },
        {
            url: 'https://bscm.jinritemai.com/api/user/login',
            shouldMatch: false,
            description: '不匹配 - 用户登录API'
        },
        {
            url: 'https://bscm.jinritemai.com/api/goods/list',
            shouldMatch: false,
            description: '不匹配 - 商品列表API'
        },
        {
            url: 'https://example.com/api/schema',
            shouldMatch: false,
            description: '不匹配 - 其他域名'
        },
        {
            url: 'https://static.jinritemai.com/images/logo.png',
            shouldMatch: false,
            description: '不匹配 - 静态资源'
        }
    ];
    
    let passedCount = 0;
    let totalCount = testCases.length;
    
    console.log(`📋 开始测试 ${totalCount} 个URL...`);
    console.log('='.repeat(80));
    
    testCases.forEach((testCase, index) => {
        const result = testUrlMatching(testCase.url);
        const passed = result === testCase.shouldMatch;
        
        if (passed) {
            passedCount++;
            console.log(`✅ 测试 ${index + 1}: ${testCase.description}`);
            console.log(`   URL: ${testCase.url}`);
            console.log(`   期望: ${testCase.shouldMatch}, 实际: ${result}`);
        } else {
            console.log(`❌ 测试 ${index + 1}: ${testCase.description}`);
            console.log(`   URL: ${testCase.url}`);
            console.log(`   期望: ${testCase.shouldMatch}, 实际: ${result}`);
        }
        console.log('');
    });
    
    console.log('='.repeat(80));
    console.log(`📊 测试结果: ${passedCount}/${totalCount} 通过`);
    
    if (passedCount === totalCount) {
        console.log('🎉 所有测试通过！过滤器工作正常。');
    } else {
        console.log('⚠️ 部分测试失败，请检查过滤器配置。');
    }
    
    return {
        passed: passedCount,
        total: totalCount,
        success: passedCount === totalCount
    };
}

// 测试URL匹配逻辑
function testUrlMatching(url) {
    // 精确匹配
    const exactUrl = 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema';
    if (url === exactUrl || url.startsWith(exactUrl)) {
        return true;
    }
    
    // 模式匹配
    const patterns = [
        /bscm\.jinritemai\.com\/fxg\/product\/tproduct\/getSchema/i,
        /bscm\.jinritemai\.com.*getSchema/i
    ];
    
    for (const pattern of patterns) {
        if (pattern.test(url)) {
            return true;
        }
    }
    
    // 关键词匹配
    const keywords = ['tproduct'];
    const urlLower = url.toLowerCase();
    
    for (const keyword of keywords) {
        if (urlLower.includes(keyword.toLowerCase()) && urlLower.includes('bscm.jinritemai.com')) {
            return true;
        }
    }
    
    return false;
}

// 模拟网络请求测试
function simulateNetworkRequest() {
    console.log('🌐 模拟网络请求测试...');
    
    const mockRequest = {
        url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123456',
        method: 'GET',
        headers: [
            { name: 'Content-Type', value: 'application/json' },
            { name: 'User-Agent', value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' },
            { name: 'Referer', value: 'https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create' }
        ],
        queryString: [
            { name: 'category_id', value: '123456' }
        ]
    };
    
    const mockResponse = {
        status: 200,
        headers: [
            { name: 'Content-Type', value: 'application/json' },
            { name: 'Server', value: 'nginx' }
        ],
        content: JSON.stringify({
            code: 0,
            message: 'success',
            data: {
                schema: {
                    required_fields: [
                        'product_name',
                        'price',
                        'category_id',
                        'brand_id',
                        'images'
                    ],
                    optional_fields: [
                        'description',
                        'specifications',
                        'tags'
                    ],
                    field_types: {
                        product_name: 'string',
                        price: 'number',
                        category_id: 'number',
                        brand_id: 'number',
                        images: 'array'
                    }
                }
            }
        })
    };
    
    console.log('📤 模拟请求:');
    console.log('URL:', mockRequest.url);
    console.log('Method:', mockRequest.method);
    console.log('Headers:', mockRequest.headers);
    
    console.log('📥 模拟响应:');
    console.log('Status:', mockResponse.status);
    console.log('Content:', mockResponse.content);
    
    // 测试过滤器是否会捕获这个请求
    const shouldCapture = testUrlMatching(mockRequest.url);
    console.log('🎯 过滤器是否会捕获:', shouldCapture ? '是' : '否');
    
    if (shouldCapture) {
        console.log('✅ 模拟请求测试通过 - 过滤器会正确捕获此请求');
    } else {
        console.log('❌ 模拟请求测试失败 - 过滤器未能捕获此请求');
    }
    
    return shouldCapture;
}

// 生成测试报告
function generateTestReport() {
    const verifyResult = verifySchemaFilter();
    const simulateResult = simulateNetworkRequest();
    
    const report = {
        timestamp: new Date().toISOString(),
        testType: 'Schema Filter Verification',
        targetUrl: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
        results: {
            urlMatching: verifyResult,
            networkSimulation: simulateResult
        },
        summary: {
            allTestsPassed: verifyResult.success && simulateResult,
            totalTests: verifyResult.total + 1,
            passedTests: verifyResult.passed + (simulateResult ? 1 : 0)
        },
        environment: {
            userAgent: navigator.userAgent,
            currentUrl: window.location.href,
            timestamp: new Date().toISOString()
        }
    };
    
    console.log('📄 生成测试报告:', report);
    return report;
}

// 导出测试报告
function exportSchemaFilterReport() {
    const report = generateTestReport();
    const jsonData = JSON.stringify(report, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `schema_filter_verification_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('📄 测试报告已导出');
}

// 暴露全局函数
window.verifySchemaFilter = verifySchemaFilter;
window.simulateNetworkRequest = simulateNetworkRequest;
window.exportSchemaFilterReport = exportSchemaFilterReport;

console.log('💡 使用以下命令进行测试:');
console.log('  verifySchemaFilter() - 验证URL过滤逻辑');
console.log('  simulateNetworkRequest() - 模拟网络请求');
console.log('  exportSchemaFilterReport() - 导出完整测试报告');
