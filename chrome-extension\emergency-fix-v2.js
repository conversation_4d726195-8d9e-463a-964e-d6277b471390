// 紧急修复脚本 v2 - 专门针对扩展不显示问题
console.log('🚨 启动紧急修复脚本 v2...');

// 立即检查和修复
(function() {
    'use strict';
    
    // 1. 检查当前环境
    console.log('📍 当前页面:', window.location.href);
    
    const isCorrectPage = window.location.href.includes('bscm.jinritemai.com') && 
                         window.location.href.includes('cargo/create');
    
    if (!isCorrectPage) {
        console.log('❌ 当前页面不正确，扩展无法工作');
        alert('请在抖音小店商品创建页面使用此扩展！');
        return;
    }
    
    console.log('✅ 页面检查通过');
    
    // 2. 强制清理现有扩展
    function cleanupExisting() {
        console.log('🧹 清理现有扩展...');
        
        // 移除现有面板
        const existingPanel = document.getElementById('douyin-extension-panel');
        if (existingPanel) {
            existingPanel.remove();
            console.log('🗑️ 移除现有面板');
        }
        
        // 清理全局对象
        if (window.douyinExtension) {
            delete window.douyinExtension;
            console.log('🗑️ 清理扩展实例');
        }
        
        if (window.douyinProductCreator) {
            delete window.douyinProductCreator;
            console.log('🗑️ 清理API对象');
        }
    }
    
    // 3. 创建简化的扩展面板
    function createEmergencyPanel() {
        console.log('🔧 创建紧急扩展面板...');
        
        const panel = document.createElement('div');
        panel.id = 'douyin-extension-panel';
        panel.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 320px !important;
            background: white !important;
            border: 2px solid #1890ff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            z-index: 999999 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            user-select: none !important;
            display: block !important;
        `;
        
        panel.innerHTML = `
            <div style="background: #1890ff; color: white; padding: 12px; border-radius: 6px 6px 0 0; font-weight: bold; cursor: move; position: relative;">
                🚀 抖音小店批量创建工具 (紧急版)
                <button id="close-panel" style="float: right; background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
            </div>
            <div style="padding: 16px;">
                <div style="margin-bottom: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #1890ff;">
                    ✅ 紧急修复成功！扩展已恢复正常工作。
                </div>
                
                <div style="margin-bottom: 12px;">
                    <button id="load-products" style="width: 100%; padding: 8px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500;">
                        📂 自动加载所有商品数据
                    </button>
                </div>

                <div id="product-status" style="margin-bottom: 12px; padding: 8px; background: #f6f6f6; border-radius: 4px; font-size: 12px;">
                    点击上方按钮自动加载suppliers目录中的真实采集商品数据
                </div>

                <div style="margin-bottom: 12px;">
                    <button id="fetch-required-fields" style="width: 100%; padding: 8px; background: #722ed1; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500; margin-bottom: 8px;">
                        📋 获取商品必填项
                    </button>
                    <button id="setup-auth-params" style="width: 100%; padding: 6px; background: #fa8c16; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🔑 设置认证参数
                    </button>
                </div>

                <div style="margin-bottom: 12px;">
                    <button id="start-batch-create" style="width: 100%; padding: 8px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500;" disabled>
                        🚀 开始批量创建
                    </button>
                </div>

                <div style="margin-bottom: 12px;">
                    <button id="reload-extension" style="width: 100%; padding: 8px; background: #f5222d; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500;">
                        🔄 重新加载完整扩展
                    </button>
                </div>

                <div id="emergency-log" style="max-height: 150px; overflow-y: auto; background: #f9f9f9; border: 1px solid #e8e8e8; border-radius: 4px; padding: 8px; font-size: 11px; font-family: monospace; display: none;">
                    <div>紧急修复日志</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        console.log('✅ 紧急面板创建成功');
        
        // 绑定事件
        bindEmergencyEvents(panel);
        
        return panel;
    }
    
    // 4. 绑定紧急面板事件
    function bindEmergencyEvents(panel) {
        console.log('🔗 绑定紧急面板事件...');
        
        // 关闭按钮
        const closeBtn = panel.querySelector('#close-panel');
        if (closeBtn) {
            closeBtn.onclick = () => {
                panel.style.display = 'none';
            };
        }
        
        // 重新加载完整扩展
        const reloadBtn = panel.querySelector('#reload-extension');
        if (reloadBtn) {
            reloadBtn.onclick = () => {
                console.log('🔄 重新加载完整扩展...');
                cleanupExisting();
                
                // 尝试重新初始化完整扩展
                setTimeout(() => {
                    if (typeof DouyinProductExtension !== 'undefined') {
                        try {
                            window.douyinExtension = new DouyinProductExtension();
                            console.log('✅ 完整扩展重新加载成功');
                            panel.remove(); // 移除紧急面板
                        } catch (error) {
                            console.error('❌ 完整扩展重新加载失败:', error);
                            alert('完整扩展加载失败，请刷新页面重试');
                        }
                    } else {
                        alert('扩展类不可用，请刷新页面重试');
                    }
                }, 500);
            };
        }
        
        // 其他按钮的基本功能
        const loadBtn = panel.querySelector('#load-products');
        if (loadBtn) {
            loadBtn.onclick = () => {
                alert('请先点击"重新加载完整扩展"按钮来启用完整功能');
            };
        }
        
        const authBtn = panel.querySelector('#setup-auth-params');
        if (authBtn) {
            authBtn.onclick = () => {
                alert('请先点击"重新加载完整扩展"按钮来启用完整功能');
            };
        }
        
        const createBtn = panel.querySelector('#start-batch-create');
        if (createBtn) {
            createBtn.onclick = () => {
                alert('请先点击"重新加载完整扩展"按钮来启用完整功能');
            };
        }
    }
    
    // 5. 执行修复
    console.log('🔧 开始执行紧急修复...');
    
    cleanupExisting();
    
    setTimeout(() => {
        const panel = createEmergencyPanel();
        
        if (panel) {
            console.log('✅ 紧急修复完成！');
            console.log('💡 请点击面板中的"重新加载完整扩展"按钮来启用所有功能');
            
            // 显示成功提示
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #52c41a;
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 1000000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            notification.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 24px; margin-bottom: 10px;">✅</div>
                    <div style="font-weight: bold; margin-bottom: 5px;">扩展修复成功！</div>
                    <div style="font-size: 12px;">请查看右上角的扩展面板</div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    }, 100);
    
})();

console.log('🚨 紧急修复脚本 v2 执行完成');
