# 商品必填项过滤功能说明

## 功能概述

本次更新为SpiderCrawl扩展添加了专门的商品必填项过滤功能，可以自动识别、过滤和保存电商平台的商品必填项相关请求。

## 主要功能

### 1. 自动识别必填项请求
- **精确URL匹配**（优先级最高）：
  - `https://bscm.jinritemai.com/fxg/product/tproduct/getSchema` - 抖音小店商品必填项Schema API
  - 支持带查询参数的URL，如：`?category_id=123&brand_id=456`

- **备用模式匹配**：
  - `/bscm\.jinritemai\.com\/fxg\/product\/tproduct\/getSchema/i`
  - `/bscm\.jinritemai\.com.*getSchema/i`
  - `/bscm\.jinritemai\.com.*tproduct/i`

- **关键词匹配**（备用）：
  - `getSchema`, `tproduct`, `fxg/product`

### 2. 支持的电商平台
- **抖音小店** (bscm.jinritemai.com) - 专门针对商品Schema API
- 可扩展支持其他平台的类似API

### 3. 数据过滤和存储
- **双重存储**：同时保存到主数据库和专门的必填项数据库
- **智能过滤**：可选择只保存必填项相关的请求
- **完整数据**：保存请求和响应的完整信息

### 4. 数据导出功能
- **JSON格式导出**：包含元数据和完整的请求响应信息
- **批量导出**：支持导出所有必填项数据
- **文件命名**：自动生成带时间戳的文件名

## 使用方法

### 1. 启用必填项过滤
1. 打开SpiderCrawl扩展面板
2. 在网络监听页面找到"启用必填项过滤"按钮
3. 点击按钮启用过滤功能
4. 按钮变为橙色表示已启用

### 2. 获取必填项数据
1. 点击"获取必填项"按钮
2. 系统会自动从数据库中筛选必填项相关的请求
3. 按钮上会显示找到的数据条数

### 3. 导出必填项数据
1. 确保已获取到必填项数据（数量 > 0）
2. 点击"导出必填项"按钮
3. 系统会自动下载JSON格式的数据文件

### 4. 查看过滤状态
- 过滤器状态会实时显示在界面上
- 启用时按钮显示为橙色
- 禁用时按钮显示为蓝色

## 数据格式说明

### 导出的JSON文件结构
```json
{
  "metadata": {
    "exportTime": "2024-01-01T12:00:00.000Z",
    "count": 10,
    "version": "1.0.0"
  },
  "data": [
    {
      "id": 1,
      "timestamp": "2024-01-01T12:00:00.000Z",
      "platform": "douyin",
      "matchType": "pattern",
      "matchValue": "/api/.*required/i",
      "url": "https://bscm.jinritemai.com/api/goods/required",
      "method": "GET",
      "status": 200,
      "request": {
        "url": "...",
        "method": "GET",
        "headers": [...],
        "queryString": [...],
        "postData": {...}
      },
      "response": {
        "status": 200,
        "headers": [...],
        "content": "...",
        "contentType": "application/json"
      },
      "metadata": {
        "savedAt": "2024-01-01T12:00:00.000Z",
        "filterVersion": "1.0.0"
      }
    }
  ]
}
```

## 配置选项

### 过滤器配置
- `autoSave`: 自动保存到文件
- `saveFormat`: 保存格式（默认JSON）
- `includeHeaders`: 包含请求头信息
- `includeRequestData`: 包含请求数据

### 存储配置
- 主数据库：`myDataStore`
- 必填项专用数据库：`requiredFieldsStore`

## 技术实现

### 核心文件
- `js/devtools.js` - 网络监听和过滤逻辑
- `js/required-fields-filter.js` - 必填项过滤器类
- `js/vue/index.js` - Vue界面逻辑
- `bgVue.html` - 用户界面

### 关键技术
- **IndexedDB**：本地数据存储
- **Chrome DevTools API**：网络请求监听
- **Vue.js + Vuetify**：用户界面
- **正则表达式**：URL模式匹配

## 注意事项

1. **权限要求**：需要`webRequest`和`storage`权限
2. **存储空间**：大量数据可能占用较多存储空间
3. **性能影响**：过滤功能可能轻微影响页面性能
4. **数据安全**：导出的数据可能包含敏感信息，请妥善保管

## 故障排除

### 常见问题
1. **过滤器不工作**
   - 检查是否已启用过滤功能
   - 确认当前页面包含目标请求
   - 查看控制台是否有错误信息

2. **数据导出失败**
   - 确认浏览器允许下载文件
   - 检查是否有必填项数据
   - 尝试刷新页面后重试

3. **数据不完整**
   - 检查网络请求是否完成
   - 确认响应内容不为空
   - 验证URL匹配规则

### 调试方法
- 打开浏览器开发者工具
- 查看Console标签页的日志信息
- 检查Network标签页的请求详情

## 更新日志

### v1.0.0 (2024-01-01)
- 新增商品必填项自动识别功能
- 支持多平台URL模式匹配
- 添加专用数据存储和导出功能
- 集成Vue.js用户界面控制
