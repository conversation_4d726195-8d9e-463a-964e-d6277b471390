// 商品必填项过滤器管理模块
// 专门用于过滤和处理商品必填项相关的网络请求

class RequiredFieldsFilter {
    constructor() {
        this.isEnabled = false;
        this.savedCount = 0;
        this.filters = {
            // 抖音小店 - 专门针对商品必填项Schema API
            douyin: {
                name: '抖音小店',
                // 精确匹配的URL
                exactUrls: [
                    'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema'
                ],
                // 模式匹配（备用）
                patterns: [
                    /bscm\.jinritemai\.com\/fxg\/product\/tproduct\/getSchema/i,
                    /bscm\.jinritemai\.com.*getSchema/i,
                    /bscm\.jinritemai\.com.*tproduct/i
                ],
                // 关键词匹配（备用）
                keywords: ['getSchema', 'tproduct', 'fxg/product']
            }
        };
        
        this.init();
    }
    
    init() {
        // 从存储中读取配置
        chrome.storage.local.get(['filterRequiredFields', 'requiredFieldsConfig'], (result) => {
            this.isEnabled = result.filterRequiredFields || false;
            this.config = result.requiredFieldsConfig || {
                autoSave: true,
                saveFormat: 'json',
                includeHeaders: true,
                includeRequestData: true
            };
        });
    }
    
    // 检查URL是否匹配必填项请求
    isRequiredFieldsRequest(url, method = 'GET') {
        console.log(`[RequiredFieldsFilter] 检查URL:`, url);

        // 检查各平台的匹配规则
        for (const [platform, config] of Object.entries(this.filters)) {

            // 1. 精确URL匹配（优先级最高）
            if (config.exactUrls) {
                for (const exactUrl of config.exactUrls) {
                    if (url === exactUrl || url.startsWith(exactUrl)) {
                        console.log(`[RequiredFieldsFilter] ✅ 精确匹配到${config.name}必填项请求:`, url);
                        return { matched: true, platform, matchType: 'exact', matchValue: exactUrl };
                    }
                }
            }

            // 2. 模式匹配
            if (config.patterns) {
                for (const pattern of config.patterns) {
                    if (pattern.test(url)) {
                        console.log(`[RequiredFieldsFilter] ✅ 模式匹配到${config.name}必填项请求:`, url);
                        return { matched: true, platform, matchType: 'pattern', matchValue: pattern.toString() };
                    }
                }
            }

            // 3. 关键词匹配
            if (config.keywords) {
                const urlLower = url.toLowerCase();
                for (const keyword of config.keywords) {
                    if (urlLower.includes(keyword.toLowerCase())) {
                        console.log(`[RequiredFieldsFilter] ✅ 关键词匹配到${config.name}必填项请求:`, url, '关键词:', keyword);
                        return { matched: true, platform, matchType: 'keyword', matchValue: keyword };
                    }
                }
            }
        }

        console.log(`[RequiredFieldsFilter] ❌ URL不匹配必填项请求:`, url);
        return { matched: false };
    }
    
    // 处理必填项请求数据
    processRequiredFieldsData(requestData) {
        const { url, method, response, request } = requestData;
        const matchResult = this.isRequiredFieldsRequest(url, method);
        
        if (!matchResult.matched) {
            return null;
        }
        
        const processedData = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            platform: matchResult.platform,
            matchType: matchResult.pattern ? 'pattern' : 'keyword',
            matchValue: matchResult.pattern || matchResult.keyword,
            url: url,
            method: method,
            status: response.status,
            request: {
                url: url,
                method: method,
                headers: this.config.includeHeaders ? request.headers : null,
                queryString: request.queryString,
                postData: this.config.includeRequestData ? request.postData : null
            },
            response: {
                status: response.status,
                headers: this.config.includeHeaders ? response.headers : null,
                content: response.content,
                contentType: this.getContentType(response.headers)
            },
            metadata: {
                savedAt: new Date().toISOString(),
                filterVersion: '1.0.0'
            }
        };
        
        return processedData;
    }
    
    // 获取响应内容类型
    getContentType(headers) {
        if (!headers) return 'unknown';
        
        const contentTypeHeader = headers.find(h => 
            h.name.toLowerCase() === 'content-type'
        );
        
        return contentTypeHeader ? contentTypeHeader.value : 'unknown';
    }
    
    // 保存数据到文件
    saveToFile(data, customFilename = null) {
        try {
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const platform = data.platform || 'unknown';
            const filename = customFilename || `required_fields_${platform}_${timestamp}.json`;
            
            const jsonData = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            // 创建下载链接
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.savedCount++;
            console.log(`[RequiredFieldsFilter] 必填项数据已保存到文件:`, filename);
            
            // 更新统计
            this.updateStats();
            
            return { success: true, filename };
        } catch (error) {
            console.error('[RequiredFieldsFilter] 保存文件失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    // 批量保存多个数据
    saveBatch(dataArray, batchName = null) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = batchName || `required_fields_batch_${timestamp}.json`;
        
        const batchData = {
            metadata: {
                batchName: batchName || 'auto_batch',
                createdAt: new Date().toISOString(),
                count: dataArray.length,
                filterVersion: '1.0.0'
            },
            data: dataArray
        };
        
        return this.saveToFile(batchData, filename);
    }
    
    // 更新统计信息
    updateStats() {
        chrome.storage.local.set({
            requiredFieldsStats: {
                savedCount: this.savedCount,
                lastSaved: new Date().toISOString()
            }
        });
    }
    
    // 启用/禁用过滤器
    setEnabled(enabled) {
        this.isEnabled = enabled;
        chrome.storage.local.set({ filterRequiredFields: enabled });
        console.log(`[RequiredFieldsFilter] 过滤器${enabled ? '已启用' : '已禁用'}`);
    }
    
    // 更新配置
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        chrome.storage.local.set({ requiredFieldsConfig: this.config });
        console.log('[RequiredFieldsFilter] 配置已更新:', this.config);
    }
    
    // 获取统计信息
    getStats() {
        return {
            isEnabled: this.isEnabled,
            savedCount: this.savedCount,
            supportedPlatforms: Object.keys(this.filters),
            config: this.config
        };
    }
    
    // 清除统计
    clearStats() {
        this.savedCount = 0;
        chrome.storage.local.remove(['requiredFieldsStats']);
        console.log('[RequiredFieldsFilter] 统计信息已清除');
    }
}

// 创建全局实例
window.requiredFieldsFilter = new RequiredFieldsFilter();
