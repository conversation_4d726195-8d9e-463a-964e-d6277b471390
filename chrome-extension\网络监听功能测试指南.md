# 网络监听功能测试指南

## 快速测试步骤

### 1. 安装扩展
1. 打开Chrome浏览器
2. 进入扩展管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择chrome-extension文件夹

### 2. 打开测试页面
1. 访问抖音小店商品创建页面：
   ```
   https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create
   ```
2. 确保已登录抖音小店账号
3. 等待页面完全加载

### 3. 验证扩展加载
1. 查看页面右上角是否出现扩展面板
2. 面板标题应显示"🚀 抖音小店批量创建工具"
3. 按钮文本应为"📋 获取商品必填项 (网络监听版)"

### 4. 测试网络监听功能

#### 方法一：使用扩展界面
1. 点击"📋 获取商品必填项 (网络监听版)"按钮
2. 观察日志输出：
   - 应显示"🚀 开始获取商品必填项（网络监听版）..."
   - 应显示"📡 启用网络监听，将自动捕获getSchema响应"
3. 等待处理完成
4. 检查下载文件夹是否有新的JSON文件

#### 方法二：使用测试脚本
1. 打开浏览器开发者工具 (F12)
2. 切换到Console标签
3. 运行测试命令：
   ```javascript
   window.networkCaptureTests.runAllTests();
   ```
4. 观察测试结果输出

### 5. 验证数据捕获

#### 检查控制台日志
应该看到类似以下的日志：
```
🔧 设置网络监听器...
✅ 网络监听器设置完成
🎯 捕获到getSchema请求: https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506
📦 获取到响应数据: {errno: 0, data: {...}}
💾 保存类目 40506 的响应数据
```

#### 检查数据存储
在控制台运行：
```javascript
// 检查捕获的数据
console.log('捕获数据数量:', window.douyinProductCreator.capturedData.size);
console.log('捕获数据详情:', Array.from(window.douyinProductCreator.capturedData.entries()));
```

#### 检查文件下载
1. 打开浏览器下载页面 (chrome://downloads/)
2. 查找以下格式的文件：
   - `categories/{类目名称}_{类目ID}_必填项.json`
   - `categories/必填项获取汇总_{时间戳}.json`

### 6. 验证文件内容

#### 单个类目文件结构
```json
{
    "categoryInfo": {
        "id": "40506",
        "name": "面巾纸",
        "level": 4,
        "path": ["个人护理", "纸品湿巾", "抽纸", "面巾纸"]
    },
    "captureTime": "2025-01-18T10:30:00.000Z",
    "requestUrl": "https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506",
    "responseData": {
        "errno": 0,
        "data": {
            "model": {
                "category_properties": {
                    "items": [...]
                }
            }
        }
    }
}
```

#### 汇总文件结构
```json
{
    "summary": {
        "totalCategories": 10,
        "successCount": 8,
        "successRate": 80,
        "errors": ["类目 xxx 未获取到响应数据"]
    },
    "capturedResponses": [
        {
            "categoryId": "40506",
            "categoryName": "面巾纸",
            "timestamp": "2025-01-18T10:30:00.000Z",
            "dataSize": 15420
        }
    ],
    "timestamp": "2025-01-18T10:30:00.000Z"
}
```

## 常见问题排查

### 1. 扩展面板不显示
- 检查是否在正确的页面 (商品创建页面)
- 刷新页面重试
- 检查扩展是否正确安装和启用

### 2. 网络监听不工作
- 检查控制台是否有错误信息
- 验证fetch方法是否被正确拦截：
  ```javascript
  console.log('原始fetch:', window.originalFetch);
  console.log('当前fetch:', window.fetch);
  ```

### 3. 没有捕获到数据
- 确保页面有类目选择操作
- 检查网络请求是否包含getSchema
- 验证类目ID提取是否正常：
  ```javascript
  // 测试URL解析
  const testUrl = 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506';
  console.log('提取的类目ID:', window.douyinProductCreator.extractCategoryIdFromUrl(testUrl));
  ```

### 4. 文件没有下载
- 检查浏览器下载权限
- 查看控制台是否有下载相关错误
- 手动测试下载功能：
  ```javascript
  // 测试文件下载
  window.douyinProductCreator.downloadFile('test.json', '{"test": "data"}');
  ```

### 5. 数据格式错误
- 检查响应数据是否为有效JSON
- 验证类目信息是否完整
- 查看错误日志了解具体问题

## 性能测试

### 内存使用
```javascript
// 监控内存使用
setInterval(() => {
    console.log('捕获数据数量:', window.douyinProductCreator.capturedData.size);
    console.log('内存使用:', performance.memory);
}, 5000);
```

### 响应时间
```javascript
// 测试响应时间
const startTime = Date.now();
window.douyinProductCreator.fetchAllRequiredFields().then(() => {
    console.log('总耗时:', Date.now() - startTime, 'ms');
});
```

## 调试技巧

### 1. 启用详细日志
在控制台设置：
```javascript
// 启用详细日志
window.douyinProductCreator.debugMode = true;
```

### 2. 手动触发测试
```javascript
// 手动测试单个功能
window.networkCaptureTests.testNetworkListener();
window.networkCaptureTests.testCategoryIdExtraction();
window.networkCaptureTests.testMockGetSchemaRequest();
```

### 3. 清理测试数据
```javascript
// 清理捕获的数据
window.douyinProductCreator.capturedData.clear();
console.log('数据已清理');
```

## 成功标准

测试通过的标准：
1. ✅ 扩展面板正常显示
2. ✅ 网络监听器成功设置
3. ✅ 能够捕获getSchema请求
4. ✅ 响应数据正确解析和存储
5. ✅ 文件自动下载成功
6. ✅ 文件内容格式正确
7. ✅ 汇总信息准确
8. ✅ 错误处理正常工作

如果所有测试都通过，说明网络监听功能已成功集成并正常工作。
