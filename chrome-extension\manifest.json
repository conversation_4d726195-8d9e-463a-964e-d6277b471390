{"manifest_version": 3, "name": "抖音小店商品批量创建助手", "version": "1.0.1", "description": "自动批量创建采集的商品到抖音小店", "permissions": ["storage", "activeTab", "scripting", "tabs", "downloads"], "host_permissions": ["https://bscm.jinritemai.com/*", "https://bscm-sso.jinritemai.com/*", "https://mssdk.bytedance.com/*", "file:///*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create*"], "js": ["a_bogus_generator.js", "products-data.js", "all-in-one.js"], "css": ["content.css"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "商品批量创建助手"}, "web_accessible_resources": [{"resources": ["a_bogus_generator.js", "all-in-one.js", "products-data.js", "debug-extension-status.js", "quick-fix.js", "test-v3-upload.js", "test-real-data.js", "test-category-selection.js", "test-drag-functionality.js", "test-category-flow.js", "test-passive-assist.js", "test-focus-handling.js", "test-required-fields.js", "test-required-fields-fix.js", "test-category-traversal.js", "test-auto-capture-schema.js", "test-file-save-structure.js", "test-fourth-level-category.js", "debug-schema-capture.js", "test-fourth-level-schema.js", "test-single-category-schema.js", "test-final-category-schema.js", "test-schema-timing.js", "test-post-request-monitoring.js", "test-category-id-matching.js", "test-schema-fallback.js", "test-listener-thread.js", "test-api-disabled.js", "test-error-handling.js", "test-network-capture.js"], "matches": ["https://bscm.jinritemai.com/*"]}]}