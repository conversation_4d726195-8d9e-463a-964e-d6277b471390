<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展状态测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .status-ok {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 抖音小店扩展状态检查</h1>
        
        <div id="status-container">
            <div class="status-item">
                <span>页面URL检查</span>
                <span id="url-status">检查中...</span>
            </div>
            <div class="status-item">
                <span>扩展面板存在</span>
                <span id="panel-status">检查中...</span>
            </div>
            <div class="status-item">
                <span>扩展实例存在</span>
                <span id="instance-status">检查中...</span>
            </div>
            <div class="status-item">
                <span>API对象存在</span>
                <span id="api-status">检查中...</span>
            </div>
            <div class="status-item">
                <span>扩展类可用</span>
                <span id="class-status">检查中...</span>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button class="btn" onclick="checkStatus()">🔄 重新检查</button>
            <button class="btn btn-success" onclick="forceInit()">🔧 强制初始化</button>
            <button class="btn btn-danger" onclick="resetExtension()">🔄 重置扩展</button>
        </div>

        <div>
            <h3>操作日志</h3>
            <div id="log" class="log"></div>
        </div>

        <div>
            <h3>快速修复</h3>
            <p>如果扩展不工作，请按顺序尝试以下操作：</p>
            <ol>
                <li>点击"重新检查"按钮</li>
                <li>如果仍有问题，点击"强制初始化"</li>
                <li>如果还是不行，点击"重置扩展"</li>
                <li>最后手段：刷新页面</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, isOk, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.parentElement.className = `status-item ${isOk ? 'status-ok' : 'status-error'}`;
        }

        function checkStatus() {
            log('🔍 开始检查扩展状态...');

            // 检查页面URL
            const url = window.location.href;
            const isCorrectPage = url.includes('bscm.jinritemai.com') && url.includes('cargo/create');
            updateStatus('url-status', isCorrectPage, isCorrectPage ? '✅ 正确' : '❌ 错误');
            log(`页面URL: ${url}`);

            // 检查扩展面板
            const panel = document.getElementById('douyin-extension-panel');
            updateStatus('panel-status', !!panel, panel ? '✅ 存在' : '❌ 不存在');

            // 检查扩展实例
            const instance = window.douyinExtension;
            updateStatus('instance-status', !!instance, instance ? '✅ 存在' : '❌ 不存在');

            // 检查API对象
            const api = window.douyinProductCreator;
            updateStatus('api-status', !!api, api ? '✅ 存在' : '❌ 不存在');

            // 检查扩展类
            const extensionClass = typeof DouyinProductExtension !== 'undefined';
            updateStatus('class-status', extensionClass, extensionClass ? '✅ 可用' : '❌ 不可用');

            log('✅ 状态检查完成');
        }

        function forceInit() {
            log('🔧 尝试强制初始化扩展...');
            
            if (typeof DouyinProductExtension !== 'undefined') {
                try {
                    // 移除现有面板
                    const existingPanel = document.getElementById('douyin-extension-panel');
                    if (existingPanel) {
                        existingPanel.remove();
                        log('🗑️ 移除现有面板');
                    }

                    // 创建新实例
                    window.douyinExtension = new DouyinProductExtension();
                    log('✅ 扩展强制初始化成功');
                    
                    setTimeout(checkStatus, 1000);
                } catch (error) {
                    log(`❌ 强制初始化失败: ${error.message}`);
                }
            } else {
                log('❌ 扩展类不可用，无法强制初始化');
            }
        }

        function resetExtension() {
            log('🔄 重置扩展...');
            
            // 清理全局对象
            if (window.douyinExtension) {
                delete window.douyinExtension;
                log('🗑️ 清理扩展实例');
            }
            
            if (window.douyinProductCreator) {
                delete window.douyinProductCreator;
                log('🗑️ 清理API对象');
            }

            // 移除面板
            const panel = document.getElementById('douyin-extension-panel');
            if (panel) {
                panel.remove();
                log('🗑️ 移除扩展面板');
            }

            log('✅ 扩展重置完成，请点击强制初始化');
            setTimeout(checkStatus, 500);
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
