// 测试网络捕获功能
// 用于验证商品必填项响应数据保存功能

console.log('🧪 开始测试网络捕获功能...');

// 测试网络监听器设置
function testNetworkListener() {
    console.log('🔧 测试网络监听器设置...');
    
    if (window.douyinProductCreator) {
        // 测试设置监听器
        window.douyinProductCreator.setupNetworkListener();
        console.log('✅ 网络监听器设置成功');
        
        // 测试数据结构
        if (window.douyinProductCreator.capturedData) {
            console.log('✅ 数据存储结构正常');
        } else {
            console.error('❌ 数据存储结构未初始化');
        }
        
        // 测试清理功能
        setTimeout(() => {
            window.douyinProductCreator.clearNetworkListener();
            console.log('✅ 网络监听器清理成功');
        }, 5000);
        
    } else {
        console.error('❌ douyinProductCreator 对象不存在');
    }
}

// 测试文件保存功能
function testFileSave() {
    console.log('💾 测试文件保存功能...');
    
    const testCategory = {
        id: 'test_40506',
        name: '测试类目_面巾纸',
        level: 4,
        path: ['个人护理', '纸品湿巾', '抽纸', '面巾纸']
    };
    
    const testResponseData = {
        url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506',
        response: {
            errno: 0,
            data: {
                model: {
                    category_properties: {
                        items: [
                            {
                                id: "1687",
                                label: "品牌",
                                required: true,
                                options: [
                                    { value_id: "596120136", value_name: "无品牌" }
                                ]
                            },
                            {
                                id: "1479",
                                label: "产品名称",
                                required: true
                            }
                        ]
                    }
                }
            }
        },
        timestamp: new Date().toISOString()
    };
    
    if (window.douyinProductCreator && window.douyinProductCreator.saveResponseToFile) {
        window.douyinProductCreator.saveResponseToFile(testCategory, testResponseData)
            .then(() => {
                console.log('✅ 文件保存测试成功');
            })
            .catch(error => {
                console.error('❌ 文件保存测试失败:', error);
            });
    } else {
        console.error('❌ saveResponseToFile 方法不存在');
    }
}

// 测试类目ID提取功能
function testCategoryIdExtraction() {
    console.log('🔍 测试类目ID提取功能...');
    
    if (window.douyinProductCreator) {
        // 测试从URL提取
        const testUrl = 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506&_bid=ffa_goods';
        const categoryId = window.douyinProductCreator.extractCategoryIdFromUrl(testUrl);
        
        if (categoryId === '40506') {
            console.log('✅ URL类目ID提取测试成功');
        } else {
            console.error('❌ URL类目ID提取测试失败，期望: 40506，实际:', categoryId);
        }
        
        // 测试从请求体提取
        const testRequestBody = {
            context: {
                category_id: "40506"
            }
        };
        const categoryIdFromBody = window.douyinProductCreator.extractCategoryIdFromRequest({
            body: JSON.stringify(testRequestBody)
        });
        
        if (categoryIdFromBody === '40506') {
            console.log('✅ 请求体类目ID提取测试成功');
        } else {
            console.error('❌ 请求体类目ID提取测试失败，期望: 40506，实际:', categoryIdFromBody);
        }
    }
}

// 模拟getSchema请求测试
function testMockGetSchemaRequest() {
    console.log('🎯 测试模拟getSchema请求...');
    
    if (window.douyinProductCreator) {
        // 设置监听器
        window.douyinProductCreator.setupNetworkListener();
        
        // 模拟一个getSchema请求
        const mockUrl = 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506';
        const mockResponse = {
            errno: 0,
            data: {
                model: {
                    category_properties: {
                        items: [
                            {
                                id: "1687",
                                label: "品牌",
                                required: true
                            }
                        ]
                    }
                }
            }
        };
        
        // 手动触发数据保存
        window.douyinProductCreator.capturedData.set('40506', {
            url: mockUrl,
            response: mockResponse,
            timestamp: new Date().toISOString()
        });
        
        console.log('✅ 模拟数据已保存到capturedData');
        console.log('📊 当前capturedData大小:', window.douyinProductCreator.capturedData.size);
        
        // 测试数据检索
        setTimeout(() => {
            if (window.douyinProductCreator.capturedData.has('40506')) {
                console.log('✅ 数据检索测试成功');
                const data = window.douyinProductCreator.capturedData.get('40506');
                console.log('📦 检索到的数据:', data);
            } else {
                console.error('❌ 数据检索测试失败');
            }
        }, 1000);
    }
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行所有测试...');
    
    testNetworkListener();
    
    setTimeout(() => {
        testCategoryIdExtraction();
    }, 1000);
    
    setTimeout(() => {
        testMockGetSchemaRequest();
    }, 2000);
    
    setTimeout(() => {
        testFileSave();
    }, 3000);
    
    setTimeout(() => {
        console.log('🎉 所有测试完成！');
    }, 5000);
}

// 页面加载完成后运行测试
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}

// 导出测试函数供手动调用
window.networkCaptureTests = {
    runAllTests,
    testNetworkListener,
    testCategoryIdExtraction,
    testMockGetSchemaRequest,
    testFileSave
};

console.log('🧪 网络捕获测试脚本加载完成');
console.log('💡 可以通过 window.networkCaptureTests.runAllTests() 手动运行测试');
