# 商品必填项网络监听功能说明

## 功能概述

基于SpiderCrawl抓包代码的思路，为抖音小店商品批量创建扩展增加了商品必填项功能的后端响应结果保存到文件的功能。

## 主要特性

### 1. 网络监听
- 自动拦截并监听 `getSchema` API请求
- 实时捕获后端响应数据
- 支持类目ID自动提取和匹配

### 2. 数据保存
- 自动保存每个类目的完整响应数据到JSON文件
- 文件命名格式：`categories/{类目名称}_{类目ID}_必填项.json`
- 包含类目信息、捕获时间、请求URL和完整响应数据

### 3. 汇总报告
- 生成处理汇总文件，包含成功率统计
- 记录所有捕获的响应信息
- 错误日志和处理状态

## 技术实现

### 网络拦截机制
```javascript
// 保存原始fetch方法
window.originalFetch = window.fetch;

// 拦截fetch请求
window.fetch = async (...args) => {
    const response = await window.originalFetch(...args);
    
    // 检查是否是getSchema请求
    if (url.includes('/getSchema')) {
        // 捕获响应数据
        const responseData = await response.clone().json();
        // 保存到capturedData Map中
    }
    
    return response;
};
```

### 数据存储结构
```javascript
capturedData: new Map() // 存储格式：
{
    categoryId: {
        url: "请求URL",
        request: "请求选项",
        response: "响应数据",
        timestamp: "时间戳"
    }
}
```

### 文件保存格式
```json
{
    "categoryInfo": {
        "id": "40506",
        "name": "面巾纸",
        "level": 4,
        "path": ["个人护理", "纸品湿巾", "抽纸", "面巾纸"]
    },
    "captureTime": "2025-01-18T10:30:00.000Z",
    "requestUrl": "https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=40506",
    "responseData": {
        "errno": 0,
        "data": {
            "model": {
                "category_properties": {
                    "items": [...]
                }
            }
        }
    }
}
```

## 使用方法

### 1. 启动功能
1. 打开抖音小店商品创建页面
2. 点击扩展面板中的"📋 获取商品必填项 (网络监听版)"按钮
3. 扩展会自动：
   - 启动网络监听
   - 遍历所有类目
   - 点击类目触发getSchema请求
   - 捕获并保存响应数据

### 2. 查看结果
- 文件会自动下载到浏览器默认下载目录
- 每个类目一个JSON文件
- 额外生成一个汇总文件

### 3. 数据验证
可以使用测试脚本验证功能：
```javascript
// 在控制台运行
window.networkCaptureTests.runAllTests();
```

## 功能优势

### 相比之前的版本
1. **完整数据捕获**：获取完整的getSchema响应，包含所有必填项信息
2. **自动化程度高**：无需手动操作，自动遍历和保存
3. **数据结构完整**：保存请求上下文和响应数据
4. **错误处理完善**：包含超时处理和错误重试机制

### 参考SpiderCrawl的优点
1. **网络拦截技术**：借鉴了SpiderCrawl的fetch拦截方法
2. **数据存储方式**：使用Map结构高效存储响应数据
3. **文件保存机制**：自动下载JSON文件到本地
4. **实时监听**：实时捕获网络请求和响应

## 配置选项

### 延迟时间配置
```javascript
delayConfig: {
    level1Click: 2500,      // 一级类目点击后等待时间
    level2Click: 2000,      // 二级类目点击后等待时间
    level3Click: 1800,      // 三级类目点击后等待时间
    level4Click: 1500,      // 四级类目点击后等待时间
    categorySwitch: 4000,   // 类目切换完成后等待时间
    betweenCategories: 4000, // 处理不同类目间的等待时间
    schemaTimeout: 20000,   // Schema响应超时时间
    stabilization: 1000     // 响应后稳定等待时间
}
```

### 超时设置
- getSchema响应等待：10秒
- 类目点击响应：根据层级2-4秒
- 整体处理超时：20秒

## 错误处理

### 常见错误及解决方案
1. **网络请求超时**：增加等待时间或重试
2. **类目元素未找到**：检查页面加载状态
3. **响应数据解析失败**：验证JSON格式
4. **文件保存失败**：检查浏览器下载权限

### 调试方法
1. 打开浏览器控制台查看日志
2. 检查capturedData Map中的数据
3. 验证网络监听器是否正常工作
4. 运行测试脚本验证各个组件

## 注意事项

1. **页面状态**：确保在正确的商品创建页面使用
2. **网络稳定**：保持网络连接稳定，避免请求失败
3. **浏览器兼容**：建议使用Chrome浏览器最新版本
4. **数据量**：大量类目处理时注意内存使用
5. **文件管理**：及时整理下载的JSON文件

## 更新日志

### v1.0.1 (2025-01-18)
- ✅ 启用网络监听功能
- ✅ 实现getSchema响应捕获
- ✅ 添加文件自动保存
- ✅ 完善错误处理机制
- ✅ 增加测试脚本
- ✅ 更新用户界面提示

### 从禁用版本的改进
- 移除了API监听禁用限制
- 恢复了完整的网络拦截功能
- 增强了数据保存和文件管理
- 改进了用户反馈和日志记录
