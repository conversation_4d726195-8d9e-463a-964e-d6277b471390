# SpiderCrawl 商品必填项过滤功能 - 快速使用指南

## 🚀 快速开始

### 1. 安装扩展
1. 打开Chrome浏览器
2. 进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `SpiderCrawlPlug-2.0.0/V2/SpiderCrawlPlug` 文件夹

### 2. 启用扩展
1. 确保扩展已启用（开关为蓝色）
2. 打开开发者工具 (F12)
3. 切换到 "SpiderCrawl" 标签页

## 📋 使用步骤

### 第一步：启用网络监听
1. 在SpiderCrawl面板中，确保网络监听开关已开启
2. 面板会显示当前的网络请求

### 第二步：启用必填项过滤
1. 点击 **"启用必填项过滤"** 按钮
2. 按钮变为橙色表示已启用
3. 现在只会保存商品必填项相关的请求

### 第三步：访问目标页面
访问抖音小店商品创建页面来触发必填项请求：
- 抖音小店商品创建页面：`https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create`
- 选择商品类目时会触发Schema API请求
- 确保页面完全加载并进行类目选择操作

### 第四步：获取必填项数据
1. 点击 **"获取必填项"** 按钮
2. 系统会自动筛选必填项相关的请求
3. 按钮上会显示找到的数据条数

### 第五步：导出数据
1. 确保已获取到数据（数量 > 0）
2. 点击 **"导出必填项"** 按钮
3. 系统会自动下载JSON格式的数据文件

## 🎯 目标请求类型

扩展会自动识别以下类型的请求：

### 精确URL匹配（主要目标）
- `https://bscm.jinritemai.com/fxg/product/tproduct/getSchema` - 抖音小店商品Schema API
- 支持带参数的URL，如：`?category_id=123456&brand_id=789`

### 备用模式匹配
- `/bscm\.jinritemai\.com\/fxg\/product\/tproduct\/getSchema/i`
- `/bscm\.jinritemai\.com.*getSchema/i`
- `/bscm\.jinritemai\.com.*tproduct/i`

### 关键词匹配（备用）
- `getSchema`, `tproduct`, `fxg/product`

## 📊 数据格式

导出的JSON文件包含：

```json
{
  "metadata": {
    "exportTime": "导出时间",
    "count": "数据条数",
    "version": "版本号"
  },
  "data": [
    {
      "url": "请求URL",
      "method": "请求方法",
      "status": "响应状态码",
      "request": "完整请求信息",
      "response": "完整响应信息"
    }
  ]
}
```

## 🔧 常用操作

### 查看实时数据
- 网络请求会实时显示在表格中
- 必填项相关的请求会被特别标记

### 搜索和过滤
- 使用搜索框过滤特定的请求
- 选择不同的请求类型进行过滤

### 数据管理
- **刷新**：重新加载数据
- **删除选中**：删除选中的请求
- **删除全部**：清空所有数据

## ⚡ 快捷操作

### 键盘快捷键
- `Ctrl + Shift + X`：快速删除所有数据

### 批量操作
- 选中多个请求后批量删除
- 批量导出选中的数据

## 🛠️ 故障排除

### 问题1：扩展不显示
**解决方案：**
- 确保已正确安装扩展
- 检查开发者工具是否已打开
- 刷新页面重试

### 问题2：没有捕获到必填项请求
**解决方案：**
- 确保已启用必填项过滤
- 检查当前页面是否包含目标请求
- 尝试刷新页面后重新操作

### 问题3：导出功能不工作
**解决方案：**
- 确认浏览器允许下载文件
- 检查是否有数据可导出
- 尝试关闭其他下载任务

### 问题4：数据不完整
**解决方案：**
- 等待网络请求完全完成
- 检查网络连接是否稳定
- 确认响应内容不为空

## 📝 使用技巧

### 1. 最佳实践
- 在访问目标页面前先启用过滤
- 定期导出数据避免丢失
- 使用搜索功能快速定位特定请求

### 2. 性能优化
- 不需要时及时关闭过滤功能
- 定期清理不需要的数据
- 避免在大量请求的页面长时间开启

### 3. 数据分析
- 导出的JSON可以用其他工具进一步分析
- 关注响应内容中的字段定义
- 比较不同平台的数据结构差异

## 🔍 测试功能

### 运行测试
在控制台中运行：
```javascript
testRequiredFieldsFilter()
```

### 导出测试报告
```javascript
exportFilterTestReport()
```

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台的错误信息
2. 检查扩展是否正确安装
3. 确认目标页面包含相关请求
4. 尝试重新安装扩展

## 📈 版本信息

- **当前版本**：v1.0.0
- **支持平台**：抖音小店、淘宝天猫、京东
- **兼容性**：Chrome 88+
- **更新时间**：2024年1月

---

**注意**：本扩展仅用于学习和研究目的，请遵守相关网站的使用条款和法律法规。
