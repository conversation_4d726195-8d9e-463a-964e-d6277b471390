// 拦截请求
const CD = chrome.devtools;
// Chrome DevTools Extension中不能使用console.log
const log = (...params) =>
    CD.inspectedWindow.eval(`console.log(...${JSON.stringify(params)})`);
let db_version = 2;
let db_name = 'SpiderCrawlData';
let spiderSwitch = null;
log('SpiderCrawl db_version:', db_version, 'db_name:', db_name)

// 商品必填项请求过滤器配置
const PRODUCT_REQUIRED_FIELDS_FILTERS = {
    // 抖音小店商品必填项Schema请求
    douyin: {
        // 精确匹配抖音小店商品Schema API
        exactUrls: [
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema'
        ],
        // 模式匹配（备用）
        patterns: [
            /bscm\.jinritemai\.com\/fxg\/product\/tproduct\/getSchema/i,
            /bscm\.jinritemai\.com.*getSchema/i
        ],
        // 关键词匹配（备用）
        keywords: ['getSchema', 'tproduct']
    }
};

// 检查URL是否匹配商品必填项请求
function isProductRequiredFieldsRequest(url, method = 'GET') {
    log('检查URL是否为必填项请求:', url);

    // 1. 精确匹配抖音小店Schema API
    for (const exactUrl of PRODUCT_REQUIRED_FIELDS_FILTERS.douyin.exactUrls) {
        if (url === exactUrl || url.startsWith(exactUrl)) {
            log('✅ 精确匹配到抖音小店必填项请求:', url);
            return true;
        }
    }

    // 2. 模式匹配（作为备用）
    for (const pattern of PRODUCT_REQUIRED_FIELDS_FILTERS.douyin.patterns) {
        if (pattern.test(url)) {
            log('✅ 模式匹配到抖音小店必填项请求:', url);
            return true;
        }
    }

    // 3. 关键词匹配（作为备用）
    const urlLower = url.toLowerCase();
    for (const keyword of PRODUCT_REQUIRED_FIELDS_FILTERS.douyin.keywords) {
        if (urlLower.includes(keyword.toLowerCase())) {
            log('✅ 关键词匹配到必填项请求:', url, '关键词:', keyword);
            return true;
        }
    }

    log('❌ URL不匹配必填项请求:', url);
    return false;
}

// 保存必填项数据到文件
function saveRequiredFieldsToFile(data, filename) {
    try {
        const jsonData = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // 通过content script下载文件
        CD.inspectedWindow.eval(`
            (function() {
                const a = document.createElement('a');
                a.href = '${url}';
                a.download = '${filename}';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL('${url}');
            })();
        `);

        log('必填项数据已保存到文件:', filename);
    } catch (error) {
        log('保存文件失败:', error);
    }
}

chrome.devtools.network.onRequestFinished.addListener(async (...args) => {
    chrome.storage.local.get(['spiderSwitch', 'filterRequiredFields'], function (result) {
        spiderSwitch = result.spiderSwitch;
        const filterRequiredFields = result.filterRequiredFields || false;

        if (spiderSwitch) {
            try {
                const [
                    {
                        // 请求的类型，查询参数，以及url
                        request: {method, queryString, url, postData},
                        response: {bodySize, status, header},
                        getContent,
                    },
                ] = args;

                var timestamp = (new Date().getTime() + 8 * 60 * 60 * 1000).toString();
                const content = await new Promise((res, rej) => getContent(res));

                // 检查是否为商品必填项请求
                const isRequiredFieldsRequest = isProductRequiredFieldsRequest(url, method);

                const data = {
                    ...args[0],
                    content: content,
                    isRequiredFields: isRequiredFieldsRequest
                };
                const query = {...data, timestamp: timestamp};

                // 如果启用了过滤且这是必填项请求，保存到专门的文件
                if (filterRequiredFields && isRequiredFieldsRequest) {
                    const filename = `required_fields_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.json`;
                    const requiredFieldsData = {
                        timestamp: timestamp,
                        url: url,
                        method: method,
                        status: status,
                        request: {
                            url: url,
                            method: method,
                            queryString: queryString,
                            postData: postData,
                            headers: args[0].request.headers
                        },
                        response: {
                            status: status,
                            headers: args[0].response.headers,
                            content: content
                        }
                    };

                    // 保存到文件
                    saveRequiredFieldsToFile(requiredFieldsData, filename);

                    log('保存商品必填项数据:', url);
                }

                // 保存到IndexedDB（如果不是仅过滤模式，或者是必填项请求）
                if (!filterRequiredFields || isRequiredFieldsRequest) {
                    const request = indexedDB.open(db_name, db_version);
                    request.onupgradeneeded = function (event) {
                        const db = event.target.result;
                        if (!db.objectStoreNames.contains("myDataStore")) {
                            db.createObjectStore("myDataStore", {
                                keyPath: "id",
                                autoIncrement: true,
                            });
                        }
                        // 创建专门的必填项数据存储
                        if (!db.objectStoreNames.contains("requiredFieldsStore")) {
                            db.createObjectStore("requiredFieldsStore", {
                                keyPath: "id",
                                autoIncrement: true,
                            });
                        }
                    };

                    request.onsuccess = function (event) {
                        const db = event.target.result;
                        const transaction = db.transaction(["myDataStore", "requiredFieldsStore"], "readwrite");

                        // 保存到主存储
                        const store = transaction.objectStore("myDataStore");
                        store.add(query);

                        // 如果是必填项请求，也保存到专门的存储
                        if (isRequiredFieldsRequest) {
                            const requiredStore = transaction.objectStore("requiredFieldsStore");
                            requiredStore.add(query);
                        }
                    };

                    request.onerror = function (event) {
                        log("SpiderCrawl onerror", event.target.errorCode);
                    };
                }

            } catch (err) {
                log(err.stack || err.toString());
            }
        }
    });
});

