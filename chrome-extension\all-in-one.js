// 抖音小店商品批量创建扩展 - 整合版本
// 所有功能整合到一个文件，避免脚本注入问题

console.log('🚀 抖音小店扩展整合版本启动...');

// 检查是否在正确的页面
if (!window.location.href.includes('bscm.jinritemai.com')) {
    console.log('⚠️ 当前不在抖音小店页面，扩展功能可能无法正常使用');
}

// 主扩展类 - 整合所有功能
class DouyinProductExtension {
    constructor() {
        this.version = 'all-in-one-v1.0';
        this.isReady = false;
        this.products = [];
        this.currentProductIndex = 0;
        this.creationResults = [];

        // 状态管理将通过api对象进行

        console.log('🔧 初始化抖音小店扩展...');
        console.log('📍 当前页面:', window.location.href);
        console.log('📦 扩展版本:', this.version);

        this.init();
    }

    // 清理所有监听器（通过api对象）
    clearWatchers() {
        if (window.douyinProductCreator && window.douyinProductCreator.clearWatchers) {
            window.douyinProductCreator.clearWatchers();
        }
    }

    // 初始化
    async init() {
        try {
            // 创建UI
            this.createUI();
            
            // 设置API对象
            this.setupAPI();
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.isReady = true;
            console.log('✅ 扩展初始化完成');
            
        } catch (error) {
            console.error('❌ 扩展初始化失败:', error);
        }
    }
    
    // 创建用户界面
    createUI() {
        // 检查是否已存在
        if (document.getElementById('douyin-extension-panel')) {
            return;
        }
        
        // 创建主面板
        const panel = document.createElement('div');
        panel.id = 'douyin-extension-panel';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border: 2px solid #1890ff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            user-select: none;
        `;
        
        panel.innerHTML = `
            <div id="panel-header" style="background: #1890ff; color: white; padding: 12px; border-radius: 6px 6px 0 0; font-weight: bold; cursor: move; position: relative;">
                🚀 抖音小店批量创建工具
                <span style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); font-size: 10px; opacity: 0.8;">可拖动</span>
                <button id="close-panel" style="float: right; background: none; border: none; color: white; cursor: pointer; font-size: 16px;">×</button>
            </div>
            <div style="padding: 16px;">
                <div style="margin-bottom: 12px;">
                    <button id="load-products" style="width: 100%; padding: 8px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500;">
                        📂 自动加载所有商品数据
                    </button>
                </div>

                <div id="product-status" style="margin-bottom: 12px; padding: 8px; background: #f6f6f6; border-radius: 4px; font-size: 12px;">
                    点击上方按钮自动加载suppliers目录中的真实采集商品数据（_detail_raw.json文件）
                </div>

                <div style="margin-bottom: 12px;">
                    <button id="fetch-required-fields" style="width: 100%; padding: 8px; background: #722ed1; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500; margin-bottom: 8px;">
                        📋 获取商品必填项 (网络监听版)
                    </button>
                    <button id="setup-auth-params" style="width: 100%; padding: 6px; background: #fa8c16; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🔑 设置认证参数
                    </button>
                </div>

                <div style="margin-bottom: 12px;">
                    <button id="start-batch-create" style="width: 100%; padding: 8px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500;" disabled>
                        🚀 开始批量创建
                    </button>
                </div>

                <div id="progress-info" style="font-size: 12px; color: #666; display: none;">
                    <div>进度: <span id="progress-text">0/0</span></div>
                    <div style="background: #f0f0f0; height: 6px; border-radius: 3px; margin: 4px 0;">
                        <div id="progress-bar" style="background: #1890ff; height: 100%; border-radius: 3px; width: 0%; transition: width 0.3s;"></div>
                    </div>
                </div>

                <div id="log-area" style="max-height: 200px; overflow-y: auto; background: #f9f9f9; border: 1px solid #e8e8e8; border-radius: 4px; padding: 8px; font-size: 11px; font-family: monospace; display: none;">
                    <div>日志输出区域</div>
                </div>



                <div style="margin-top: 12px; text-align: center;">
                    <button id="toggle-log" style="background: none; border: 1px solid #d9d9d9; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 11px;">
                        显示/隐藏日志
                    </button>
                </div>
                <div style="margin-bottom: 12px;">
                    <button id="auto-capture-schema" style="width: 100%; padding: 8px; background: #f5222d; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500;">
                        🤖 自动遍历类目并抓取Schema
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);

        // 添加拖动相关的CSS样式
        this.addDragStyles();

        // 添加拖动功能
        this.makePanelDraggable(panel);

        // 绑定UI事件
        this.bindUIEvents();

        console.log('✅ UI界面创建完成');
    }

    // 添加拖动相关的CSS样式
    addDragStyles() {
        // 检查是否已添加样式
        if (document.getElementById('douyin-drag-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'douyin-drag-styles';
        style.textContent = `
            #douyin-extension-panel {
                transition: box-shadow 0.2s ease;
            }

            #douyin-extension-panel:hover {
                box-shadow: 0 6px 16px rgba(0,0,0,0.2);
            }

            #panel-header {
                transition: background-color 0.2s ease;
            }

            #panel-header:hover {
                background: #40a9ff !important;
            }

            #panel-header:active {
                background: #096dd9 !important;
            }

            /* 拖动时的样式 */
            .dragging {
                box-shadow: 0 8px 24px rgba(0,0,0,0.3) !important;
                transform: rotate(1deg);
            }

            /* 防止拖动时选中文本 */
            .no-select {
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }

            /* 拖动提示动画 */
            @keyframes dragHint {
                0%, 100% { opacity: 0.8; }
                50% { opacity: 1; }
            }

            #panel-header span {
                animation: dragHint 2s infinite;
            }
        `;

        document.head.appendChild(style);
        console.log('✅ 拖动样式已添加');
    }

    // 使面板可拖动
    makePanelDraggable(panel) {
        const header = panel.querySelector('#panel-header');
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        // 获取面板当前位置
        const rect = panel.getBoundingClientRect();
        xOffset = rect.left;
        yOffset = rect.top;

        // 鼠标按下事件
        header.addEventListener('mousedown', (e) => {
            // 如果点击的是关闭按钮，不启动拖动
            if (e.target.id === 'close-panel') {
                return;
            }

            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                header.style.cursor = 'grabbing';
                panel.style.transition = 'none'; // 拖动时禁用过渡动画
                panel.classList.add('dragging'); // 添加拖动样式
                document.body.classList.add('no-select'); // 防止选中文本
            }
        });

        // 鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();

                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                // 限制拖动范围，确保面板不会完全移出视窗
                const maxX = window.innerWidth - panel.offsetWidth;
                const maxY = window.innerHeight - panel.offsetHeight;

                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));

                xOffset = currentX;
                yOffset = currentY;

                panel.style.left = currentX + 'px';
                panel.style.top = currentY + 'px';
                panel.style.right = 'auto'; // 移除right定位
            }
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                header.style.cursor = 'move';
                panel.style.transition = ''; // 恢复过渡动画
                panel.classList.remove('dragging'); // 移除拖动样式
                document.body.classList.remove('no-select'); // 恢复文本选择

                // 保存位置到本地存储
                this.savePanelPosition(currentX, currentY);

                // 显示位置保存提示
                this.showPositionSavedHint(panel);
            }
        });

        // 添加触摸设备支持
        this.addTouchSupport(header, panel);

        // 加载保存的位置
        this.loadPanelPosition(panel);

        console.log('✅ 面板拖动功能已启用（支持鼠标和触摸）');
    }

    // 添加触摸设备支持
    addTouchSupport(header, panel) {
        let touchStartX, touchStartY;
        let panelStartX, panelStartY;
        let isTouching = false;

        // 触摸开始
        header.addEventListener('touchstart', (e) => {
            if (e.target.id === 'close-panel') {
                return;
            }

            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;

            const rect = panel.getBoundingClientRect();
            panelStartX = rect.left;
            panelStartY = rect.top;

            isTouching = true;
            panel.classList.add('dragging');
            panel.style.transition = 'none';

            e.preventDefault(); // 防止页面滚动
        }, { passive: false });

        // 触摸移动
        document.addEventListener('touchmove', (e) => {
            if (!isTouching) return;

            const touch = e.touches[0];
            const deltaX = touch.clientX - touchStartX;
            const deltaY = touch.clientY - touchStartY;

            let newX = panelStartX + deltaX;
            let newY = panelStartY + deltaY;

            // 限制拖动范围
            const maxX = window.innerWidth - panel.offsetWidth;
            const maxY = window.innerHeight - panel.offsetHeight;

            newX = Math.max(0, Math.min(newX, maxX));
            newY = Math.max(0, Math.min(newY, maxY));

            panel.style.left = newX + 'px';
            panel.style.top = newY + 'px';
            panel.style.right = 'auto';

            e.preventDefault(); // 防止页面滚动
        }, { passive: false });

        // 触摸结束
        document.addEventListener('touchend', (e) => {
            if (!isTouching) return;

            isTouching = false;
            panel.classList.remove('dragging');
            panel.style.transition = '';

            // 保存位置
            const rect = panel.getBoundingClientRect();
            this.savePanelPosition(rect.left, rect.top);
            this.showPositionSavedHint(panel);
        });

        console.log('✅ 触摸拖动支持已添加');
    }

    // 保存面板位置
    savePanelPosition(x, y) {
        try {
            localStorage.setItem('douyin-panel-position', JSON.stringify({ x, y }));
            console.log(`✅ 面板位置已保存: (${x}, ${y})`);
        } catch (error) {
            console.warn('保存面板位置失败:', error);
        }
    }

    // 显示位置保存提示
    showPositionSavedHint(panel) {
        // 创建提示元素
        const hint = document.createElement('div');
        hint.style.cssText = `
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        hint.textContent = '位置已保存';

        panel.style.position = 'relative';
        panel.appendChild(hint);

        // 显示提示
        setTimeout(() => {
            hint.style.opacity = '1';
        }, 10);

        // 隐藏并移除提示
        setTimeout(() => {
            hint.style.opacity = '0';
            setTimeout(() => {
                if (hint.parentNode) {
                    hint.parentNode.removeChild(hint);
                }
            }, 300);
        }, 1500);
    }

    // 加载面板位置
    loadPanelPosition(panel) {
        try {
            const savedPosition = localStorage.getItem('douyin-panel-position');
            if (savedPosition) {
                const { x, y } = JSON.parse(savedPosition);

                // 确保位置在有效范围内
                const maxX = window.innerWidth - panel.offsetWidth;
                const maxY = window.innerHeight - panel.offsetHeight;

                const validX = Math.max(0, Math.min(x, maxX));
                const validY = Math.max(0, Math.min(y, maxY));

                panel.style.left = validX + 'px';
                panel.style.top = validY + 'px';
                panel.style.right = 'auto';

                console.log(`✅ 已恢复面板位置: (${validX}, ${validY})`);
            }
        } catch (error) {
            console.warn('加载面板位置失败:', error);
        }
    }

    // 绑定UI事件
    bindUIEvents() {
        // 关闭面板
        document.getElementById('close-panel').onclick = () => {
            document.getElementById('douyin-extension-panel').style.display = 'none';
        };
        
        // 加载商品数据
        document.getElementById('load-products').onclick = () => {
            this.loadProductData();
        };
        
        // 获取商品必填项
        document.getElementById('fetch-required-fields').onclick = async () => {
            const button = document.getElementById('fetch-required-fields');
            const originalText = button.textContent;

            try {
                button.disabled = true;
                button.textContent = '🔄 正在获取...';

                this.log('🚀 开始获取商品必填项（网络监听版）...', 'info');
                this.log('📡 启用网络监听，将自动捕获getSchema响应', 'info');

                // 使用新的整合功能
                if (window.douyinProductCreator && window.douyinProductCreator.fetchAllRequiredFields) {
                    const result = await window.douyinProductCreator.fetchAllRequiredFields();

                    if (result.success) {
                        this.log(`🎉 获取完成！处理了 ${result.processedCount} 个类目`, 'success');
                        this.log(`💾 成功保存 ${result.successCount} 个文件 (成功率: ${result.successRate}%)`, 'success');
                        this.log(`📡 网络监听捕获了 ${result.capturedResponses.length} 个响应`, 'success');

                        if (result.errors && result.errors.length > 0) {
                            this.log(`⚠️ 有 ${result.errors.length} 个类目处理失败`, 'warning');
                            result.errors.forEach(error => {
                                this.log(`   - ${error}`, 'error');
                            });
                        }

                        this.log('📁 必填项数据文件已自动下载到categories目录', 'info');
                        this.log('📊 汇总信息文件也已生成', 'info');
                        this.log('💡 每个类目的完整响应数据都已保存', 'info');

                    } else {
                        const errorMsg = result.error || result.message || '未知错误';
                        this.log('❌ 获取必填项失败: ' + errorMsg, 'error');

                        // 如果有具体的错误列表，也显示出来
                        if (result.errors && result.errors.length > 0) {
                            result.errors.forEach(error => {
                                this.log(`   - ${error}`, 'error');
                            });
                        }

                        this.log('💡 请检查网络连接和页面状态', 'info');
                    }
                } else {
                    // 方法不存在时也不使用备用方法
                    this.log('❌ 获取必填项功能不可用', 'error');
                    this.log('💡 请确保扩展正确加载', 'info');
                }

            } catch (error) {
                this.log('❌ 获取商品必填项失败: ' + error.message, 'error');
                console.error('获取商品必填项失败:', error);
            } finally {
                button.disabled = false;
                button.textContent = originalText;
            }
        };

        // 设置认证参数
        document.getElementById('setup-auth-params').onclick = () => {
            this.setupAuthParams();
        };

        // 开始批量创建
        document.getElementById('start-batch-create').onclick = () => {
            this.startBatchCreate();
        };

        // 切换日志显示
        document.getElementById('toggle-log').onclick = () => {
            const logArea = document.getElementById('log-area');
            logArea.style.display = logArea.style.display === 'none' ? 'block' : 'none';
        };

        document.getElementById('auto-capture-schema').onclick = async () => {
            const button = document.getElementById('auto-capture-schema');
            const originalText = button.textContent;

            try {
                // 检查API是否已初始化
                if (!window.douyinProductCreator || !window.douyinProductCreator.autoCaptureAllCategorySchemas) {
                    alert('API未初始化，请刷新页面重试');
                    return;
                }

                // 确认用户操作
                const confirmed = confirm('即将开始自动抓取所有类目的Schema数据，这可能需要较长时间。是否继续？');
                if (!confirmed) {
                    return;
                }

                // 禁用按钮并显示进度
                button.disabled = true;
                button.textContent = '🔄 正在抓取...';

                this.log('🚀 开始自动抓取所有类目Schema...', 'info');

                // 执行抓取
                await window.douyinProductCreator.autoCaptureAllCategorySchemas();

                this.log('🎉 Schema抓取任务完成！', 'success');

            } catch (error) {
                console.error('自动抓取Schema失败:', error);
                this.log('❌ Schema抓取失败: ' + error.message, 'error');
                alert('抓取失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = originalText;
            }
        };

    }









    // 设置API对象
    setupAPI() {
        const api = {
            version: this.version,

            // 监听器管理
            brandPageWatcher: null,
            categoryPageWatcher: null,
            isCreatingProduct: false,

            // 工具方法：延迟执行
            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },

            // 清理所有监听器
            clearWatchers() {
                if (this.brandPageWatcher) {
                    clearInterval(this.brandPageWatcher);
                    this.brandPageWatcher = null;
                    console.log('🧹 清除品牌页面监听器');
                }

                if (this.categoryPageWatcher) {
                    clearInterval(this.categoryPageWatcher);
                    this.categoryPageWatcher = null;
                    console.log('🧹 清除类目页面监听器');
                }
            },

            // 获取token方法
            getMsToken() {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'msToken' && value) {
                        console.log('[调试] getMsToken 获取到:', value);
                        return value;
                    }
                }
                console.warn('[调试] getMsToken 未获取到 msToken');
                return '';
            },
            
            getCsrfToken() {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if ((name === 'csrf_session_id' || name === 'passport_csrf_token') && value) {
                        console.log('[调试] getCsrfToken 获取到:', name, value);
                        return value;
                    }
                }
                console.warn('[调试] getCsrfToken 未获取到 csrf_token');
                return '';
            },
            
            getVerifyFp() {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 's_v_web_id' && value) {
                        console.log('[调试] getVerifyFp 获取到:', value);
                        return value;
                    }
                }
                console.warn('[调试] getVerifyFp 未获取到 s_v_web_id');
                return 'verify_default_fp';
            },
            
            testConnection() {
                const msToken = this.getMsToken();
                const csrfToken = this.getCsrfToken();
                const verifyFp = this.getVerifyFp();
                console.log('[调试] testConnection 检查结果:', { msToken, csrfToken, verifyFp });
                return {
                    ready: !!(msToken && csrfToken)
                };
            },
            
            // V3版本：生成a_bogus参数 - 基于Python版本的_generate_a_bogus_v2
            generateABogusV2() {
                // 基于Python版本的实现：hashlib.md5(f"{time.time()}{random.randint(1000, 9999)}".encode()).hexdigest()[:32]
                const timestamp = Date.now() / 1000; // 转换为秒
                const randomNum = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
                const data = `${timestamp}${randomNum}`;

                // 简化的MD5实现（用于生成a_bogus）
                return this.simpleMD5(data).substring(0, 32);
            },

            // 简化的MD5实现
            simpleMD5(str) {
                // 这是一个简化版本，实际项目中建议使用专业的MD5库
                let hash = 0;
                if (str.length === 0) return hash.toString(16);
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // 转换为32位整数
                }
                return Math.abs(hash).toString(16).padStart(8, '0').repeat(4).substring(0, 32);
            },

            // V3版本：获取verifyFp参数 - 基于Python版本的_get_verify_fp_v2
            getVerifyFpV2() {
                // 从cookies中获取s_v_web_id或使用默认值
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 's_v_web_id' && value) {
                        return value;
                    }
                }
                return 'verify_default_fp';
            },

            // 保持原有的简化版本作为备用
            generateABogus() {
                return `${Date.now()}${Math.random().toString(36).substring(2, 16)}`;
            },
            
            // V3版本：上传单张图片 - 基于Python版本的upload_product_images_v2
            async uploadSingleImage(imageFile) {
                try {
                    console.log(`📤 上传图片: ${imageFile.name} (${imageFile.size} bytes)`);

                    // 构建上传URL - 完全基于Python版本
                    const baseUrl = 'https://bscm.jinritemai.com/fxg/product/img/batchupload';

                    // 构建URL参数 - 与Python版本完全一致
                    const params = {
                        '_bid': 'ffa_goods',
                        'msToken': this.getMsToken(),
                        'a_bogus': this.generateABogusV2(),
                        'verifyFp': this.getVerifyFpV2(),
                        'fp': this.getVerifyFpV2()
                    };

                    // 构建完整URL
                    const urlParams = new URLSearchParams(params);
                    const uploadUrl = `${baseUrl}?${urlParams.toString()}`;

                    // 准备上传数据 - 使用Python版本的格式
                    const formData = new FormData();
                    formData.append('image[0]', imageFile, 'image.jpg');

                    // 构建请求头 - 基于Python版本的_get_upload_headers_v2
                    const headers = {
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Origin': 'https://bscm.jinritemai.com',
                        'Referer': 'https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'X-Ecom-Platform-Source': 'uscmSupplyDistritionDpu',
                        'x-secsdk-csrf-token': this.getCsrfToken()
                    };

                    console.log('📡 发送图片上传请求...');

                    // 发送请求
                    const response = await fetch(uploadUrl, {
                        method: 'POST',
                        headers: headers,
                        body: formData,
                        credentials: 'include'
                    });

                    console.log(`📊 上传响应状态: ${response.status}`);

                    if (response.status === 200) {
                        try {
                            const uploadData = await response.json();
                            console.log('📋 上传响应数据:', uploadData);

                            // 检查响应格式 - 与Python版本一致
                            if (typeof uploadData === 'object' && uploadData.errno === 0) {
                                // 提取上传后的图片URL - data是字符串列表
                                const data = uploadData.data || [];
                                if (Array.isArray(data) && data.length > 0) {
                                    const uploadedUrl = data[0]; // 直接取第一个元素，它就是URL字符串
                                    if (uploadedUrl) {
                                        console.log(`✅ 图片上传成功: ${uploadedUrl}`);
                                        return uploadedUrl;
                                    } else {
                                        console.error('❌ 上传响应中没有找到图片URL');
                                        return null;
                                    }
                                } else {
                                    console.error('❌ 上传响应格式异常:', uploadData);
                                    return null;
                                }
                            } else {
                                const errorMsg = uploadData.msg || uploadData.errmsg || '未知错误';
                                console.error(`❌ 图片上传失败: ${errorMsg}`);
                                return null;
                            }
                        } catch (jsonError) {
                            console.error('❌ 解析上传响应失败:', jsonError);
                            return null;
                        }
                    } else {
                        console.error(`❌ 图片上传请求失败: ${response.status}`);
                        return null;
                    }

                } catch (error) {
                    console.error('❌ 上传图片异常:', error);
                    return null;
                }
            },
            
            // V3版本：批量上传图片 - 完全基于Python版本的upload_product_images_v2
            async uploadImages(imageUrls) {
                try {
                    // 检查是否还在创建当前商品
                    if (!this.isCreatingProduct) {
                        console.warn('⚠️ 商品创建已停止，跳过图片上传');
                        return [];
                    }

                    console.log(`🖼️ 开始上传 ${imageUrls.length} 张图片到抖音平台...`);
                    const uploadedImages = [];

                    for (let i = 0; i < imageUrls.length; i++) {
                        // 再次检查创建状态
                        if (!this.isCreatingProduct) {
                            console.warn('⚠️ 商品创建已停止，中断图片上传');
                            break;
                        }

                        const imageUrl = imageUrls[i];
                        console.log(`📤 上传第 ${i+1}/${imageUrls.length} 张图片...`);

                        try {
                            // 检查是否是抖音平台的图片 - 与Python版本一致
                            if (imageUrl.includes('ecombdimg.com')) {
                                // 如果已经是抖音平台的图片，直接使用
                                uploadedImages.push(imageUrl);
                                console.log(`✅ 图片 ${i+1} 已是抖音平台图片，直接使用`);
                                continue;
                            }

                            // 下载图片 - 与Python版本一致
                            console.log(`⬇️ 下载图片: ${imageUrl}`);
                            const response = await fetch(imageUrl, { timeout: 30000 });
                            if (!response.ok) {
                                console.error(`❌ 下载图片失败: ${imageUrl} - 状态码: ${response.status}`);
                                continue;
                            }

                            // 转换为文件对象
                            const blob = await response.blob();
                            const imageFile = new File([blob], 'image.jpg', {
                                type: blob.type || 'image/jpeg'
                            });

                            // 上传到抖音平台
                            const uploadedUrl = await this.uploadSingleImage(imageFile);
                            if (uploadedUrl) {
                                uploadedImages.push(uploadedUrl);
                                console.log(`✅ 图片 ${i+1} 上传成功`);
                            } else {
                                console.error(`❌ 图片 ${i+1} 上传失败`);
                            }

                            // 添加延迟避免请求过快
                            if (i < imageUrls.length - 1) {
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            }

                        } catch (error) {
                            console.error(`❌ 上传图片异常: ${error}`);
                            continue;
                        }
                    }

                    if (uploadedImages.length > 0) {
                        console.log(`✅ 成功上传 ${uploadedImages.length} 张图片`);
                        return uploadedImages;
                    } else {
                        console.error('❌ 没有成功上传任何图片');
                        return [];
                    }

                } catch (error) {
                    console.error('❌ 图片上传过程异常:', error);
                    return [];
                }
            },
            
            // 创建商品 - 包含页面元素填充
            async createProduct(productData) {
                try {
                    console.log('🚀 开始创建商品:', productData.name);

                    // 设置全局产品数据，供后续自动处理使用
                    window.currentProductData = productData;

                    // 第一步：上传图片
                    console.log('📸 第一步：上传商品图片');
                    const uploadedImages = await this.uploadImages(productData.images || []);

                    if (uploadedImages.length === 0) {
                        throw new Error('图片上传失败');
                    }

                    console.log(`✅ 图片上传完成，共 ${uploadedImages.length} 张`);

                    // 第二步：填充页面元素并启动监听器
                    console.log('📝 第二步：填充页面元素');
                    await this.fillPageElements(productData, uploadedImages);

                    // 第三步：等待用户完成整个商品创建流程
                    console.log('⏳ 第三步：等待商品创建流程完成...');
                    console.log('💡 提示：请按照页面提示完成类目选择、品牌选择等步骤');
                    console.log('🎯 当看到"继续创建下一个商品"按钮时，表示当前商品创建完成');

                    return {
                        success: true,
                        result: '商品信息填充完成，等待用户操作',
                        uploadedImages: uploadedImages,
                        productData: productData
                    };

                } catch (error) {
                    throw error;
                }
            },

            // 填充页面元素 - 被动辅助模式
            async fillPageElements(productData, uploadedImages) {
                try {
                    console.log('🔍 开始辅助填充页面元素...');

                    // 检查是否还在创建当前商品
                    if (!this.isCreatingProduct) {
                        console.warn('⚠️ 商品创建已停止，跳过页面元素填充');
                        return;
                    }

                    // 首先清理之前的监听器，确保不会冲突
                    console.log('🧹 清理之前的监听器...');
                    this.clearWatchers();

                    // 第一步：填充商品图片到页面控件并等待完成
                    console.log('🖼️ 第一步：填充商品图片到页面控件...');
                    await this.fillProductImages(uploadedImages);

                    // 第二步：等待图片在页面上完全显示和加载
                    console.log('⏳ 第二步：等待图片在页面上完全显示和加载...');
                    const imageLoadResult = await this.waitForImagesLoaded(uploadedImages.length);
                    console.log('📊 图片加载结果:', imageLoadResult);

                    // 第三步：图片加载完成后再填充商品标题
                    console.log('📝 第三步：图片加载完成，开始填充商品标题...');
                    const titleResult = await this.fillProductTitle(productData.name);

                    // 第四步：等待标题焦点处理完成和页面响应
                    if (titleResult && titleResult.focusHandled) {
                        console.log('⏳ 第四步：等待标题焦点处理完成，类目应该正在显示...');
                        console.log('📋 标题填充结果:', titleResult);
                        await new Promise(resolve => setTimeout(resolve, 3000)); // 增加等待时间到3秒
                    } else {
                        console.log('⏳ 第四步：等待页面响应...');
                        await new Promise(resolve => setTimeout(resolve, 2000)); // 增加等待时间到2秒
                    }

                    // 检查页面状态并提供提示
                    const pageStatus = this.checkCurrentPageStatus();
                    console.log('📊 页面填充状态:', pageStatus);

                    // 被动辅助类目选择 - 不主动点击按钮
                    console.log('🏷️ 准备辅助类目选择...');
                    await this.assistCategorySelection(productData);

                    // 监听品牌和产品名称页面
                    console.log('🏷️ 开始监听品牌和产品名称页面...');
                    this.watchForBrandAndProductPage(productData);

                    // 提供用户操作提示
                    this.showUserGuidance(pageStatus);

                    console.log('✅ 页面元素辅助填充完成');

                } catch (error) {
                    console.error('❌ 页面元素填充失败:', error);
                    throw error;
                }
            },

            // 显示用户操作指导
            showUserGuidance(pageStatus) {
                console.log('💡 用户操作指导:');

                if (!pageStatus.hasTitle) {
                    console.log('  ⚠️ 商品标题未填充，请检查标题输入框');
                }

                if (pageStatus.imageCount < 5) {
                    console.log(`  ⚠️ 商品图片不足，当前 ${pageStatus.imageCount}/5 张`);
                }

                if (pageStatus.hasTitle && pageStatus.imageCount >= 5) {
                    if (pageStatus.nextButtonEnabled) {
                        console.log('  ✅ 必填项已完成，可以点击"下一步"按钮进入类目选择');
                    } else {
                        console.log('  ⏳ 页面正在处理中，请稍等下一步按钮变为可用状态');
                    }
                }

                if (pageStatus.isOnCategoryPage) {
                    console.log('  🏷️ 已在类目选择页面，系统将自动辅助选择类目');
                }
            },

            // 辅助类目选择 - 被动等待用户操作
            async assistCategorySelection(productData) {
                try {
                    console.log('🔍 准备辅助类目选择...');

                    // 检查当前页面状态，但不主动跳转
                    const pageStatus = this.checkCurrentPageStatus();
                    console.log('📄 当前页面状态:', pageStatus);

                    // 等待类目显示（标题失焦后类目会显示）
                    console.log('⏳ 等待类目显示（标题失焦触发）...');
                    await this.waitForCategoryDisplay();

                    // 检查是否有"手动选择"按钮
                    console.log('🔍 查找"手动选择"按钮...');
                    const manualSelectButton = this.findManualSelectButton();
                    if (manualSelectButton) {
                        console.log('🎯 发现"手动选择"按钮，准备点击进入类目选择');

                        // 滚动到按钮位置
                        manualSelectButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await new Promise(resolve => setTimeout(resolve, 500));

                        // 点击"手动选择"按钮
                        manualSelectButton.click();
                        console.log('✅ 已点击"手动选择"按钮');

                        // 等待类目选择窗口出现
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // 开始类目选择
                        await this.selectProductCategory(productData);

                    } else if (this.isOnCategorySelectionPage()) {
                        console.log('✅ 检测到类目选择页面，准备辅助选择');
                        await this.selectProductCategory(productData);
                    } else {
                        // 检查是否有类目相关元素显示
                        console.log('🔍 检查是否有类目相关元素显示...');
                        const hasCategoryElements = this.checkCategoryElementsVisible();
                        if (hasCategoryElements) {
                            console.log('✅ 检测到类目元素显示，开始辅助选择');
                            await this.selectProductCategory(productData);
                        } else {
                            // 不在类目页面，提示用户操作
                            console.log('ℹ️ 未找到"手动选择"按钮，类目尚未显示');
                            console.log('💡 提示: 请手动点击"下一步"按钮进入类目选择页面');

                            // 提供页面状态信息
                            console.log('📊 当前页面信息:');
                            console.log(`  - URL: ${window.location.href}`);
                            console.log(`  - 标题: ${document.title}`);
                            console.log(`  - 页面文本包含"类目": ${document.body.textContent.includes('类目')}`);
                            console.log(`  - 页面文本包含"手动选择": ${document.body.textContent.includes('手动选择')}`);

                            // 监听页面变化，当进入类目页面时自动辅助
                            this.watchForCategoryPage(productData);
                        }
                    }

                } catch (error) {
                    console.error('❌ 辅助类目选择失败:', error);
                }
            },

            // 等待类目显示（标题失焦后触发）
            async waitForCategoryDisplay() {
                console.log('⏳ 等待类目显示...');

                const maxWaitTime = 30000; // 最多等待30秒
                const checkInterval = 2000; // 每2秒检查一次
                const startTime = Date.now();
                let focusAttempts = 0;
                const maxFocusAttempts = 5;
                let hasTriedFocusOperations = false;

                // 首先尝试重复焦点操作来触发类目显示
                console.log('🔄 开始重复焦点操作来触发类目显示...');

                while (Date.now() - startTime < maxWaitTime) {
                    // 如果还没尝试过焦点操作，先进行焦点操作
                    if (!hasTriedFocusOperations && focusAttempts < maxFocusAttempts) {
                        console.log(`🔄 第${focusAttempts + 1}次尝试触发类目显示（重复焦点操作）...`);

                        // 查找标题输入框
                        const titleInput = document.querySelector('input[placeholder*="标题"], input[placeholder*="名称"], input[type="text"]:first-of-type');
                        if (titleInput) {
                            // 获取焦点
                            console.log('👆 获取标题输入框焦点...');
                            titleInput.focus();
                            await new Promise(resolve => setTimeout(resolve, 300));

                            // 失去焦点
                            console.log('👇 失去标题输入框焦点...');
                            titleInput.blur();
                            await new Promise(resolve => setTimeout(resolve, 800));

                            console.log(`✅ 完成第${focusAttempts + 1}次焦点操作`);
                        } else {
                            console.warn('⚠️ 未找到标题输入框，跳过焦点操作');
                        }

                        focusAttempts++;

                        // 如果完成了所有焦点操作，标记为已尝试
                        if (focusAttempts >= maxFocusAttempts) {
                            hasTriedFocusOperations = true;
                            console.log('✅ 完成所有焦点操作，开始检查类目显示...');
                        }
                    }

                    // 检查是否有"手动选择"按钮出现
                    const manualButton = this.findManualSelectButton();
                    if (manualButton) {
                        console.log('✅ 发现"手动选择"按钮，类目推荐已显示');
                        return true;
                    }

                    // 检查是否有类目相关元素出现
                    if (this.checkCategoryElementsVisible()) {
                        console.log('✅ 类目元素已显示');
                        return true;
                    }

                    // 检查是否进入类目选择页面
                    if (this.isOnCategorySelectionPage()) {
                        console.log('✅ 已进入类目选择页面');
                        return true;
                    }

                    await new Promise(resolve => setTimeout(resolve, checkInterval));

                    const elapsedTime = Date.now() - startTime;
                    if (elapsedTime % 5000 === 0) {
                        console.log(`⏳ 继续等待类目显示... (${Math.floor(elapsedTime/1000)}s)`);
                    }
                }

                console.log('⏰ 等待类目显示超时');
                return false;
            },

            // 查找"手动选择"按钮
            findManualSelectButton() {
                console.log('🔍 开始查找"手动选择"按钮...');

                const manualSelectSelectors = [
                    'button:contains("手动选择")',
                    'button[class*="link"]:contains("手动选择")',
                    '.ecom-g-btn-link:contains("手动选择")',
                    'a:contains("手动选择")',
                    'span:contains("手动选择")',
                    'div:contains("手动选择")'
                ];

                // 先检查页面上是否有包含"手动选择"文本的元素
                const allElements = document.querySelectorAll('*');
                let foundElements = [];
                for (const element of allElements) {
                    if (element.textContent && element.textContent.includes('手动选择')) {
                        foundElements.push({
                            tag: element.tagName,
                            text: element.textContent.trim(),
                            className: element.className,
                            id: element.id
                        });
                    }
                }

                if (foundElements.length > 0) {
                    console.log('📋 页面上包含"手动选择"的元素:', foundElements);
                } else {
                    console.log('⚠️ 页面上没有找到包含"手动选择"的元素');

                    // 检查是否有类目相关的元素
                    const categoryKeywords = ['类目', '分类', '选择', '推荐'];
                    for (const keyword of categoryKeywords) {
                        const elements = Array.from(document.querySelectorAll('*')).filter(el =>
                            el.textContent && el.textContent.includes(keyword) && el.offsetWidth > 0
                        );
                        if (elements.length > 0) {
                            console.log(`📋 找到包含"${keyword}"的元素:`, elements.length, '个');
                        }
                    }
                }

                for (const selector of manualSelectSelectors) {
                    try {
                        if (selector.includes(':contains')) {
                            // 处理包含文本的选择器
                            const text = selector.match(/:contains\("(.+)"\)/)[1];
                            const baseSelector = selector.replace(/:contains\("(.+)"\)/, '');
                            const elements = document.querySelectorAll(baseSelector || 'button, a, span, div');

                            for (const element of elements) {
                                if (element.textContent.includes(text) &&
                                    element.offsetWidth > 0 && element.offsetHeight > 0 &&
                                    !element.disabled) {
                                    console.log(`🎯 找到"手动选择"按钮: ${selector}`);
                                    console.log(`📋 按钮信息:`, {
                                        tag: element.tagName,
                                        text: element.textContent.trim(),
                                        className: element.className,
                                        id: element.id
                                    });
                                    return element;
                                }
                            }
                        } else {
                            const element = document.querySelector(selector);
                            if (element && element.offsetWidth > 0 && !element.disabled) {
                                return element;
                            }
                        }
                    } catch (e) {
                        console.warn(`选择器 ${selector} 查找失败:`, e);
                    }
                }

                console.log('❌ 未找到"手动选择"按钮');
                return null;
            },

            // 检查类目元素是否可见
            checkCategoryElementsVisible() {
                const categorySelectors = [
                    '.category-selector',
                    '.category-select',
                    '.category-picker',
                    '.category-search',
                    'input[placeholder*="搜索类目"]',
                    'input[placeholder*="请输入类目"]',
                    '.category-tree',
                    '.category-list',
                    '.goods-category',
                    '.product-category',
                    '.ecom-category',
                    '[data-testid*="category"]',
                    '[class*="category"]'
                ];

                for (const selector of categorySelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        for (const element of elements) {
                            if (element.offsetWidth > 0 && element.offsetHeight > 0) {
                                console.log(`🎯 发现可见的类目元素: ${selector}`);
                                return true;
                            }
                        }
                    } catch (e) {
                        // 忽略选择器错误
                    }
                }

                // 检查页面文本内容
                const bodyText = document.body.textContent || '';
                const categoryKeywords = ['选择类目', '商品类目', '请选择类目', '类目搜索', '分类选择'];
                for (const keyword of categoryKeywords) {
                    if (bodyText.includes(keyword)) {
                        console.log(`🎯 页面内容包含类目关键词: ${keyword}`);
                        return true;
                    }
                }

                return false;
            },

            // 监听页面变化，等待进入类目选择页面
            watchForCategoryPage(productData) {
                console.log('👀 开始监听类目选择页面...');

                // 清除之前的监听器（避免重复监听）
                if (this.categoryPageWatcher) {
                    clearInterval(this.categoryPageWatcher);
                    console.log('🧹 清除之前的类目页面监听器');
                }

                this.categoryPageWatcher = setInterval(() => {
                    // 检查是否还在创建当前商品
                    if (!this.isCreatingProduct) {
                        console.log('🛑 商品创建已完成，停止类目页面监听');
                        clearInterval(this.categoryPageWatcher);
                        this.categoryPageWatcher = null;
                        return;
                    }

                    if (this.isOnCategorySelectionPage()) {
                        console.log('✅ 检测到进入类目选择页面，开始辅助选择');
                        clearInterval(this.categoryPageWatcher);
                        this.categoryPageWatcher = null;

                        // 延迟一下确保页面完全加载
                        setTimeout(() => {
                            this.selectProductCategory(productData);
                        }, 1000);
                    }
                }, 1000);

                // 30秒后停止监听
                setTimeout(() => {
                    if (this.categoryPageWatcher) {
                        clearInterval(this.categoryPageWatcher);
                        this.categoryPageWatcher = null;
                        console.log('⏰ 类目页面监听超时，停止监听');
                    }
                }, 30000);
            },

            // 检查当前页面状态
            checkCurrentPageStatus() {
                const status = {
                    url: window.location.href,
                    title: document.title,
                    hasTitle: false,
                    imageCount: 0,
                    nextButtonEnabled: false,
                    isOnCategoryPage: false
                };

                // 检查标题
                const titleInput = this.findTitleInput();
                if (titleInput && titleInput.value.trim().length > 0) {
                    status.hasTitle = true;
                }

                // 检查图片（使用同步版本保持兼容性）
                status.imageCount = this.getUploadedImageCountSync();

                // 检查下一步按钮状态
                const nextButton = this.findNextStepButtonSync();
                if (nextButton && !nextButton.disabled) {
                    status.nextButtonEnabled = true;
                }

                // 检查是否在类目页面
                status.isOnCategoryPage = this.isOnCategorySelectionPage();

                return status;
            },

            // 填充商品标题 - 基于抖音小店页面结构，包含焦点处理
            async fillProductTitle(title) {
                try {
                    console.log('📝 填充商品标题:', title);

                    // 基于抖音小店页面的标题输入框选择器
                    const titleSelectors = [
                        '#pg-title-input', // 抖音小店商品标题输入框ID
                        'input[placeholder*="请输入2-60个字符"]',
                        'input[placeholder*="商品标题"]',
                        'input[id*="title"]',
                        'input[class*="ecom-g-input"]',
                        '.ecom-g-input-affix-wrapper input',
                        'input[placeholder*="标题"]',
                        'input[placeholder*="商品名称"]'
                    ];

                    let titleInput = null;

                    // 尝试找到标题输入框
                    for (const selector of titleSelectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            for (const element of elements) {
                                // 检查元素是否可见且可编辑
                                if (element.offsetWidth > 0 && element.offsetHeight > 0 &&
                                    !element.disabled && !element.readOnly) {
                                    titleInput = element;
                                    console.log(`✅ 找到标题输入框: ${selector}`);
                                    break;
                                }
                            }
                            if (titleInput) break;
                        } catch (e) {
                            console.warn(`选择器 ${selector} 查找失败:`, e);
                        }
                    }

                    if (titleInput) {
                        console.log('🎯 开始标题填充流程...');

                        // 第一步：聚焦输入框
                        console.log('1️⃣ 聚焦标题输入框');
                        titleInput.focus();
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // 第二步：清空现有内容
                        console.log('2️⃣ 清空现有内容');
                        titleInput.value = '';
                        titleInput.dispatchEvent(new Event('input', { bubbles: true }));
                        titleInput.dispatchEvent(new Event('change', { bubbles: true }));
                        await new Promise(resolve => setTimeout(resolve, 300));

                        // 第三步：设置新标题
                        console.log('3️⃣ 设置新标题内容');
                        titleInput.value = title;

                        // 触发输入事件
                        titleInput.dispatchEvent(new Event('input', { bubbles: true }));
                        titleInput.dispatchEvent(new Event('change', { bubbles: true }));

                        // 模拟键盘事件以确保React组件更新
                        titleInput.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true }));
                        titleInput.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));

                        await new Promise(resolve => setTimeout(resolve, 500));

                        // 第四步：失去焦点以触发类目显示
                        console.log('4️⃣ 失去焦点以触发类目显示');
                        titleInput.blur();

                        // 等待页面响应焦点变化
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        console.log('✅ 商品标题填充完成，开始重复焦点操作来触发页面加载');

                        // 第五步：重复进行焦点操作来确保页面加载
                        const maxFocusAttempts = 5;
                        console.log(`🔄 开始执行${maxFocusAttempts}次焦点操作来触发页面加载...`);

                        for (let i = 1; i <= maxFocusAttempts; i++) {
                            console.log(`🔄 第${i}次焦点操作 - 获取标题输入框焦点...`);
                            titleInput.focus();
                            await new Promise(resolve => setTimeout(resolve, 600));

                            console.log(`👇 第${i}次焦点操作 - 失去标题输入框焦点...`);
                            titleInput.blur();
                            await new Promise(resolve => setTimeout(resolve, 1000));

                            console.log(`✅ 完成第${i}次焦点操作`);

                            // 每次焦点操作后等待一下，但不检查类目（避免中断）
                            if (i < maxFocusAttempts) {
                                console.log(`⏳ 等待页面响应第${i}次焦点操作...`);
                                await new Promise(resolve => setTimeout(resolve, 500));
                            }
                        }

                        console.log(`✅ 完成所有${maxFocusAttempts}次焦点操作`);

                        // 所有焦点操作完成后，再检查类目显示
                        console.log('🔍 检查类目是否已经显示...');
                        const hasCategory = this.checkCategoryElementsVisible();
                        if (hasCategory) {
                            console.log('🎯 焦点操作完成后检测到类目显示');
                        } else {
                            console.log('⚠️ 焦点操作完成后未检测到类目显示，继续等待...');
                        }

                        // 最终等待页面完全加载
                        console.log('⏳ 等待页面完全加载完毕...');
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // 验证填充结果
                        setTimeout(() => {
                            if (titleInput.value === title) {
                                console.log('✅ 标题填充验证成功');
                                console.log('🏷️ 类目应该已经显示，准备辅助选择');
                            } else {
                                console.warn('⚠️ 标题填充验证失败，当前值:', titleInput.value);
                            }
                        }, 500);

                        // 返回成功状态，包含焦点处理完成的信息
                        return {
                            success: true,
                            titleInput: titleInput,
                            focusHandled: true,
                            message: '标题填充完成，已处理焦点变化'
                        };

                    } else {
                        console.warn('⚠️ 未找到标题输入框');

                        // 尝试通过页面结构分析查找
                        const allInputs = document.querySelectorAll('input[type="text"], input:not([type])');
                        console.log(`页面共有 ${allInputs.length} 个文本输入框`);

                        allInputs.forEach((input, index) => {
                            console.log(`输入框 ${index + 1}:`, {
                                id: input.id,
                                placeholder: input.placeholder,
                                className: input.className,
                                visible: input.offsetWidth > 0 && input.offsetHeight > 0
                            });
                        });
                    }

                } catch (error) {
                    console.error('❌ 填充商品标题失败:', error);
                    throw error;
                }
            },

            // 填充商品图片 - 基于抖音小店页面结构
            async fillProductImages(uploadedImages) {
                try {
                    console.log('🖼️ 填充商品图片:', uploadedImages.length, '张');

                    // 基于抖音小店页面的图片上传区域选择器
                    const imageSelectors = [
                        'input[type="file"][accept*="image"]', // 通用图片上传
                        '.ecom-g-upload input[type="file"]', // 抖音小店上传组件
                        '.ant-upload input[type="file"]', // Ant Design上传组件
                        '[class*="upload"] input[type="file"]',
                        '.upload-area input[type="file"]',
                        '.image-upload input[type="file"]'
                    ];

                    let uploadInput = null;

                    // 尝试找到图片上传输入框
                    for (const selector of imageSelectors) {
                        try {
                            const elements = document.querySelectorAll(selector);
                            for (const element of elements) {
                                // 检查是否是主图上传区域（通常在页面上方）
                                const rect = element.getBoundingClientRect();
                                if (rect.top < window.innerHeight / 2) { // 在页面上半部分
                                    uploadInput = element;
                                    console.log(`✅ 找到主图上传区域: ${selector}`);
                                    break;
                                }
                            }
                            if (uploadInput) break;
                        } catch (e) {
                            console.warn(`选择器 ${selector} 查找失败:`, e);
                        }
                    }

                    if (uploadInput) {
                        console.log('📤 尝试通过文件上传控件设置图片');

                        // 方法1：尝试模拟文件上传
                        await this.simulateImageUpload(uploadInput, uploadedImages);

                        // 方法2：尝试直接设置图片URL
                        await this.setImageUrlsToPage(uploadedImages);

                    } else {
                        console.warn('⚠️ 未找到图片上传控件，尝试其他方法');

                        // 分析页面结构
                        const allFileInputs = document.querySelectorAll('input[type="file"]');
                        console.log(`页面共有 ${allFileInputs.length} 个文件上传控件`);

                        allFileInputs.forEach((input, index) => {
                            const rect = input.getBoundingClientRect();
                            console.log(`文件上传控件 ${index + 1}:`, {
                                accept: input.accept,
                                className: input.className,
                                position: { top: rect.top, left: rect.left },
                                visible: rect.width > 0 && rect.height > 0
                            });
                        });

                        // 尝试直接设置图片URL
                        await this.setImageUrlsToPage(uploadedImages);
                    }

                    console.log('✅ 商品图片填充完成');

                } catch (error) {
                    console.error('❌ 填充商品图片失败:', error);
                    throw error;
                }
            },

            // 模拟图片上传
            async simulateImageUpload(uploadInput, uploadedImages) {
                try {
                    console.log('🔄 模拟图片上传...');

                    // 由于uploadedImages已经是抖音平台的URL，我们需要创建虚拟文件
                    const files = [];

                    for (let i = 0; i < Math.min(uploadedImages.length, 5); i++) {
                        const imageUrl = uploadedImages[i];

                        try {
                            // 下载图片并创建File对象
                            const response = await fetch(imageUrl);
                            const blob = await response.blob();
                            const file = new File([blob], `image_${i + 1}.jpg`, { type: 'image/jpeg' });
                            files.push(file);
                        } catch (error) {
                            console.warn(`下载图片 ${i + 1} 失败:`, error);
                        }
                    }

                    if (files.length > 0) {
                        // 创建DataTransfer对象
                        const dataTransfer = new DataTransfer();
                        files.forEach(file => dataTransfer.items.add(file));

                        // 设置文件到input
                        uploadInput.files = dataTransfer.files;

                        // 触发change事件
                        uploadInput.dispatchEvent(new Event('change', { bubbles: true }));

                        console.log(`✅ 模拟上传了 ${files.length} 张图片`);
                    }

                } catch (error) {
                    console.error('❌ 模拟图片上传失败:', error);
                }
            },

            // 设置图片URL到页面 - 基于抖音小店页面结构
            async setImageUrlsToPage(uploadedImages) {
                try {
                    console.log('🔗 尝试设置图片URL到抖音小店页面...');

                    // 抖音小店图片预览区域选择器
                    const imageContainers = [
                        '.ecom-g-upload-list', // 抖音小店上传列表
                        '.ant-upload-list', // Ant Design上传列表
                        '.upload-list',
                        '.image-list',
                        '.pic-list',
                        '[class*="upload-list"]',
                        '[class*="image-preview"]'
                    ];

                    let containerFound = false;

                    for (const containerSelector of imageContainers) {
                        const container = document.querySelector(containerSelector);
                        if (container) {
                            console.log(`✅ 找到图片容器: ${containerSelector}`);
                            containerFound = true;

                            // 清空现有图片
                            container.innerHTML = '';

                            // 创建图片预览元素
                            uploadedImages.forEach((imageUrl, index) => {
                                const imgWrapper = document.createElement('div');
                                imgWrapper.className = 'uploaded-image-item';
                                imgWrapper.style.cssText = `
                                    display: inline-block;
                                    margin: 5px;
                                    border: 1px solid #d9d9d9;
                                    border-radius: 4px;
                                    overflow: hidden;
                                    position: relative;
                                `;

                                const imgElement = document.createElement('img');
                                imgElement.src = imageUrl;
                                imgElement.style.cssText = `
                                    width: 100px;
                                    height: 100px;
                                    object-fit: cover;
                                    display: block;
                                `;
                                imgElement.alt = `商品图片${index + 1}`;

                                // 添加图片序号
                                const indexLabel = document.createElement('div');
                                indexLabel.textContent = index + 1;
                                indexLabel.style.cssText = `
                                    position: absolute;
                                    top: 2px;
                                    left: 2px;
                                    background: rgba(0,0,0,0.6);
                                    color: white;
                                    padding: 2px 6px;
                                    border-radius: 2px;
                                    font-size: 12px;
                                `;

                                imgWrapper.appendChild(imgElement);
                                imgWrapper.appendChild(indexLabel);
                                container.appendChild(imgWrapper);
                            });

                            break;
                        }
                    }

                    if (!containerFound) {
                        console.warn('⚠️ 未找到图片容器，尝试创建预览区域');
                        await this.createImagePreviewArea(uploadedImages);
                    }

                    // 尝试通过页面API设置图片
                    await this.trySetImagesThroughPageAPI(uploadedImages);

                    console.log('✅ 图片URL设置完成');

                } catch (error) {
                    console.error('❌ 设置图片URL失败:', error);
                }
            },

            // 创建图片预览区域
            async createImagePreviewArea(uploadedImages) {
                try {
                    console.log('🎨 创建图片预览区域...');

                    // 查找合适的位置插入预览区域
                    const targetSelectors = [
                        '.ecom-g-upload',
                        '.ant-upload',
                        '[class*="upload"]',
                        'form',
                        'body'
                    ];

                    let targetElement = null;
                    for (const selector of targetSelectors) {
                        targetElement = document.querySelector(selector);
                        if (targetElement) break;
                    }

                    if (targetElement) {
                        const previewArea = document.createElement('div');
                        previewArea.id = 'douyin-image-preview';
                        previewArea.style.cssText = `
                            margin: 10px 0;
                            padding: 10px;
                            border: 2px dashed #1890ff;
                            border-radius: 6px;
                            background: #f6ffed;
                        `;

                        const title = document.createElement('div');
                        title.textContent = `✅ 已上传 ${uploadedImages.length} 张商品图片`;
                        title.style.cssText = `
                            margin-bottom: 10px;
                            font-weight: bold;
                            color: #52c41a;
                        `;

                        const imageContainer = document.createElement('div');
                        imageContainer.style.cssText = 'display: flex; flex-wrap: wrap; gap: 10px;';

                        uploadedImages.forEach((imageUrl, index) => {
                            const imgElement = document.createElement('img');
                            imgElement.src = imageUrl;
                            imgElement.style.cssText = `
                                width: 80px;
                                height: 80px;
                                object-fit: cover;
                                border-radius: 4px;
                                border: 1px solid #d9d9d9;
                            `;
                            imgElement.title = `商品图片 ${index + 1}`;
                            imageContainer.appendChild(imgElement);
                        });

                        previewArea.appendChild(title);
                        previewArea.appendChild(imageContainer);

                        // 插入到目标元素之后
                        targetElement.parentNode.insertBefore(previewArea, targetElement.nextSibling);

                        console.log('✅ 图片预览区域创建完成');
                    }

                } catch (error) {
                    console.error('❌ 创建图片预览区域失败:', error);
                }
            },

            // 尝试通过页面API设置图片
            async trySetImagesThroughPageAPI(uploadedImages) {
                try {
                    console.log('🔌 尝试通过页面API设置图片...');

                    // 尝试各种可能的全局方法
                    const apiMethods = [
                        'setProductImages',
                        'updateProductImages',
                        'uploadImages',
                        'setMainImages'
                    ];

                    for (const method of apiMethods) {
                        if (typeof window[method] === 'function') {
                            try {
                                await window[method](uploadedImages);
                                console.log(`✅ 通过 ${method} 设置图片成功`);
                                break;
                            } catch (error) {
                                console.warn(`⚠️ ${method} 调用失败:`, error);
                            }
                        }
                    }

                    // 触发自定义事件
                    window.dispatchEvent(new CustomEvent('douyinProductImagesUploaded', {
                        detail: {
                            images: uploadedImages,
                            count: uploadedImages.length,
                            timestamp: Date.now()
                        }
                    }));

                    // 尝试通过React/Vue组件更新
                    if (window.React || window.Vue) {
                        console.log('🔄 检测到前端框架，尝试触发组件更新...');

                        // 触发可能的状态更新
                        setTimeout(() => {
                            const event = new Event('input', { bubbles: true });
                            const inputs = document.querySelectorAll('input[type="file"]');
                            inputs.forEach(input => input.dispatchEvent(event));
                        }, 100);
                    }

                } catch (error) {
                    console.error('❌ 通过页面API设置图片失败:', error);
                }
            },

            // 验证页面必填项是否完成
            async validateRequiredFields() {
                try {
                    console.log('🔍 验证页面必填项...');

                    const result = {
                        isValid: true,
                        missingFields: [],
                        completedFields: []
                    };

                    // 检查商品标题
                    const titleInput = this.findTitleInput();
                    if (titleInput) {
                        if (titleInput.value.trim().length === 0) {
                            result.missingFields.push('商品标题');
                            result.isValid = false;
                        } else {
                            result.completedFields.push('商品标题');
                        }
                    }

                    // 检查商品图片（使用异步版本等待页面更新）
                    const imageCount = await this.getUploadedImageCount();
                    if (imageCount < 5) {
                        result.missingFields.push(`商品图片 (当前${imageCount}/5)`);
                        result.isValid = false;
                    } else {
                        result.completedFields.push('商品图片');
                    }

                    // 检查其他可能的必填项
                    const requiredSelectors = [
                        'input[required]',
                        'select[required]',
                        'textarea[required]',
                        '.required input',
                        '.required select',
                        '.required textarea'
                    ];

                    for (const selector of requiredSelectors) {
                        const elements = document.querySelectorAll(selector);
                        for (const element of elements) {
                            if (element.offsetWidth > 0 && element.offsetHeight > 0) {
                                const isEmpty = !element.value || element.value.trim() === '';
                                const fieldName = element.placeholder || element.name || element.id || '未知字段';

                                if (isEmpty) {
                                    result.missingFields.push(fieldName);
                                    result.isValid = false;
                                } else {
                                    result.completedFields.push(fieldName);
                                }
                            }
                        }
                    }

                    console.log('📊 必填项验证结果:', result);
                    return result;

                } catch (error) {
                    console.error('❌ 验证必填项失败:', error);
                    return { isValid: false, missingFields: ['验证失败'], completedFields: [] };
                }
            },

            // 查找标题输入框
            findTitleInput() {
                const titleSelectors = [
                    '#pg-title-input',
                    'input[placeholder*="请输入2-60个字符"]',
                    'input[placeholder*="商品标题"]',
                    'input[id*="title"]',
                    'input[class*="ecom-g-input"]',
                    '.ecom-g-input-affix-wrapper input',
                    'input[placeholder*="标题"]'
                ];

                for (const selector of titleSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        for (const element of elements) {
                            if (element.offsetWidth > 0 && element.offsetHeight > 0 &&
                                !element.disabled && !element.readOnly) {
                                return element;
                            }
                        }
                    } catch (e) {
                        // 忽略选择器错误
                    }
                }
                return null;
            },

            // 获取已上传图片数量（带重试机制）
            async getUploadedImageCount(retryCount = 0, maxRetries = 5) {
                try {
                    // 抖音小店图片上传区域的选择器
                    const imageSelectors = [
                        '.upload-list-item',
                        '.ant-upload-list-item',
                        '.ecom-upload-list-item',
                        '.upload-item',
                        'img[src*="blob:"]',
                        'img[src*="data:image"]',
                        'img[src*="ecombdimg.com"]', // 抖音平台图片
                        '.image-preview',
                        '.uploaded-image',
                        '.ecom-g-upload-list-item',
                        '.ant-upload-list-picture-card-container'
                    ];

                    let maxCount = 0;
                    for (const selector of imageSelectors) {
                        const elements = document.querySelectorAll(selector);
                        const visibleElements = Array.from(elements).filter(el =>
                            el.offsetWidth > 0 && el.offsetHeight > 0
                        );
                        maxCount = Math.max(maxCount, visibleElements.length);
                    }

                    console.log(`📸 检测到 ${maxCount} 张已上传图片 (尝试 ${retryCount + 1}/${maxRetries + 1})`);

                    // 如果检测到的图片数量少于5张，且还有重试次数，则等待后重试
                    if (maxCount < 5 && retryCount < maxRetries) {
                        console.log(`⏳ 图片数量不足5张，等待页面更新后重试...`);
                        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
                        return await this.getUploadedImageCount(retryCount + 1, maxRetries);
                    }

                    return maxCount;

                } catch (error) {
                    console.error('❌ 获取图片数量失败:', error);
                    return 0;
                }
            },

            // 等待图片在页面上完全加载
            async waitForImagesLoaded(expectedCount = 5, maxWaitTime = 30000) {
                try {
                    console.log(`⏳ 等待 ${expectedCount} 张图片在页面上完全加载...`);

                    const startTime = Date.now();
                    let lastImageCount = 0;
                    let stableCount = 0;

                    while (Date.now() - startTime < maxWaitTime) {
                        // 检查当前图片数量
                        const currentImageCount = await this.getUploadedImageCount();

                        // 如果图片数量达到预期，再等待一段时间确保稳定
                        if (currentImageCount >= expectedCount) {
                            if (currentImageCount === lastImageCount) {
                                stableCount++;
                                if (stableCount >= 3) { // 连续3次检测都是相同数量，认为稳定
                                    console.log(`✅ ${currentImageCount} 张图片已稳定加载完成`);
                                    return {
                                        success: true,
                                        imageCount: currentImageCount,
                                        waitTime: Date.now() - startTime
                                    };
                                }
                            } else {
                                stableCount = 0; // 重置稳定计数
                            }
                        } else {
                            stableCount = 0; // 重置稳定计数
                        }

                        lastImageCount = currentImageCount;

                        // 等待2秒后再次检查
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        console.log(`📊 图片加载进度: ${currentImageCount}/${expectedCount} (等待时间: ${Math.round((Date.now() - startTime) / 1000)}s)`);
                    }

                    // 超时但仍返回当前状态
                    const finalImageCount = await this.getUploadedImageCount();
                    console.warn(`⚠️ 图片加载等待超时，当前图片数量: ${finalImageCount}/${expectedCount}`);

                    return {
                        success: finalImageCount >= expectedCount,
                        imageCount: finalImageCount,
                        waitTime: maxWaitTime,
                        timeout: true
                    };

                } catch (error) {
                    console.error('❌ 等待图片加载失败:', error);
                    return {
                        success: false,
                        imageCount: 0,
                        waitTime: 0,
                        error: error.message
                    };
                }
            },

            // 同步版本的图片数量检测（保持向后兼容）
            getUploadedImageCountSync() {
                try {
                    const imageSelectors = [
                        '.upload-list-item',
                        '.ant-upload-list-item',
                        '.ecom-upload-list-item',
                        '.upload-item',
                        'img[src*="blob:"]',
                        'img[src*="data:image"]',
                        'img[src*="ecombdimg.com"]',
                        '.image-preview',
                        '.uploaded-image',
                        '.ecom-g-upload-list-item',
                        '.ant-upload-list-picture-card-container'
                    ];

                    let maxCount = 0;
                    for (const selector of imageSelectors) {
                        const elements = document.querySelectorAll(selector);
                        const visibleElements = Array.from(elements).filter(el =>
                            el.offsetWidth > 0 && el.offsetHeight > 0
                        );
                        maxCount = Math.max(maxCount, visibleElements.length);
                    }

                    return maxCount;

                } catch (error) {
                    console.error('❌ 获取图片数量失败:', error);
                    return 0;
                }
            },

            // 查找下一步按钮 (异步版本)
            async findNextStepButton() {
                return this.findNextStepButtonSync();
            },

            // 查找下一步按钮 (同步版本)
            findNextStepButtonSync() {
                const nextButtonSelectors = [
                    'button[type="submit"]',
                    'button.primary',
                    'button[class*="primary"]',
                    'button[class*="btn-primary"]',
                    '.ecom-btn-primary',
                    '.ecom-button-primary'
                ];

                const buttonTexts = ['下一步', '继续', '下一页', '提交', '确定', '保存并继续'];

                for (const selector of nextButtonSelectors) {
                    try {
                        const buttons = document.querySelectorAll(selector);
                        for (const button of buttons) {
                            if (button.offsetWidth > 0 && button.offsetHeight > 0) {
                                const buttonText = button.textContent.trim();
                                const isNextButton = buttonTexts.some(text => buttonText.includes(text));

                                if (isNextButton) {
                                    return button;
                                }
                            }
                        }
                    } catch (e) {
                        console.warn(`选择器 ${selector} 查找失败:`, e);
                    }
                }

                return null;
            },

            // 查找类目选择按钮
            async findCategorySelectionButton() {
                const categoryButtonSelectors = [
                    'button:contains("选择类目")',
                    'button:contains("商品类目")',
                    'a[href*="category"]',
                    '.category-btn',
                    '.select-category',
                    '.category-selector-btn'
                ];

                for (const selector of categoryButtonSelectors) {
                    try {
                        if (selector.includes(':contains')) {
                            const text = selector.match(/:contains\("(.+)"\)/)[1];
                            const buttons = document.querySelectorAll('button, a');
                            for (const btn of buttons) {
                                if (btn.textContent.includes(text) &&
                                    btn.offsetWidth > 0 && !btn.disabled) {
                                    return btn;
                                }
                            }
                        } else {
                            const button = document.querySelector(selector);
                            if (button && button.offsetWidth > 0 && !button.disabled) {
                                return button;
                            }
                        }
                    } catch (e) {
                        console.warn(`类目按钮选择器 ${selector} 查找失败:`, e);
                    }
                }

                return null;
            },

            // 自动选择商品类目 - 基于_category_info文件
            async selectProductCategory(productData) {
                try {
                    console.log('🏷️ 开始自动选择商品类目...');

                    // 获取类目信息
                    const categoryInfo = await this.getCategoryInfoFromCache(productData);
                    if (!categoryInfo) {
                        console.warn('⚠️ 未找到类目信息，跳过类目选择');
                        return;
                    }

                    console.log('📋 获取到类目信息:', categoryInfo);

                    // 检查是否在类目选择页面
                    if (!this.isOnCategorySelectionPage()) {
                        console.log('🔍 当前不在类目选择页面，尝试进入...');

                        // 尝试多种方式进入类目选择页面
                        const success = await this.navigateToCategorySelection();

                        if (!success) {
                            console.warn('⚠️ 未能进入类目选择页面，可能需要先完成其他步骤');
                            return;
                        }
                    }

                    // 等待类目弹窗完全加载
                    console.log('⏳ 等待类目弹窗完全加载...');
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 执行智能推荐类目选择
                    await this.searchAndSelectCategory(categoryInfo);

                    console.log('✅ 类目选择完成');

                } catch (error) {
                    console.error('❌ 类目选择失败:', error);
                }
            },

            // 导航到类目选择页面（暂时禁用自动点击，避免意外触发）
            async navigateToCategorySelection() {
                try {
                    console.log('🚀 navigateToCategorySelection 方法已暂时禁用，避免自动点击"下一步"按钮');
                    console.log('💡 提示：请手动点击"下一步"按钮进入类目选择页面');
                    return false;

                    // 方法2: 查找类目选择相关的链接或按钮
                    const categoryButtons = [
                        'button:contains("选择类目")',
                        'button:contains("商品类目")',
                        'a[href*="category"]',
                        '.category-btn',
                        '.select-category'
                    ];

                    for (const selector of categoryButtons) {
                        try {
                            let button = null;
                            if (selector.includes(':contains')) {
                                const text = selector.match(/:contains\("(.+)"\)/)[1];
                                const buttons = document.querySelectorAll('button, a');
                                for (const btn of buttons) {
                                    if (btn.textContent.includes(text) && !btn.disabled) {
                                        button = btn;
                                        break;
                                    }
                                }
                            } else {
                                button = document.querySelector(selector);
                            }

                            if (button && !button.disabled) {
                                console.log(`🎯 找到类目按钮: ${selector}`);
                                button.click();
                                await new Promise(resolve => setTimeout(resolve, 2000));

                                if (this.isOnCategorySelectionPage()) {
                                    console.log('✅ 成功进入类目选择页面');
                                    return true;
                                }
                            }
                        } catch (e) {
                            console.warn(`类目按钮 ${selector} 点击失败:`, e);
                        }
                    }

                    // 方法3: 检查是否已经在类目选择相关的界面
                    const categoryElements = document.querySelectorAll([
                        '.category-tree',
                        '.category-selector',
                        '.category-search',
                        '[placeholder*="搜索类目"]',
                        '[placeholder*="请输入类目"]'
                    ].join(','));

                    if (categoryElements.length > 0) {
                        console.log('✅ 检测到类目选择元素，可能已在类目选择界面');
                        return true;
                    }

                    console.log('⚠️ 所有导航方法都失败了');
                    return false;

                } catch (error) {
                    console.error('❌ 导航到类目选择页面失败:', error);
                    return false;
                }
            },

            // 从缓存文件获取类目信息
            async getCategoryInfoFromCache(productData) {
                try {
                    // 方法1: 优先尝试通过background script读取_category_info文件（最准确的数据源）
                    try {
                        console.log('🔍 尝试从_category_info文件读取类目信息...');
                        const response = await new Promise((resolve, reject) => {
                            chrome.runtime.sendMessage(
                                { action: 'getCategoryInfo', productData: productData },
                                (response) => {
                                    if (chrome.runtime.lastError) {
                                        reject(new Error(chrome.runtime.lastError.message));
                                    } else {
                                        resolve(response);
                                    }
                                }
                            );
                        });

                        if (response.success && response.categoryInfo && response.categoryInfo.labelDesc) {
                            console.log(`✅ 从_category_info文件获取到labelDesc: ${response.categoryInfo.labelDesc}`);
                            return {
                                labelDesc: response.categoryInfo.labelDesc,
                                source: '_category_info_file'
                            };
                        } else {
                            console.log('⚠️ _category_info文件中未找到有效的labelDesc');
                        }
                    } catch (bgError) {
                        console.warn('❌ 从_category_info文件读取失败:', bgError);
                    }

                    // 方法2: 从supplier_info中获取labelDesc
                    if (productData.supplier_info && productData.supplier_info.erpLabelsJson) {
                        const categoryLabel = productData.supplier_info.erpLabelsJson.find(
                            label => label.labelName === '主营类目' && label.labelDesc
                        );

                        if (categoryLabel) {
                            console.log(`从supplier_info获取到labelDesc: ${categoryLabel.labelDesc}`);
                            return {
                                labelDesc: categoryLabel.labelDesc,
                                source: 'supplier_info'
                            };
                        }
                    }

                    // 方法3: 从category字段获取（作为备用）
                    if (productData.category) {
                        console.log(`⚠️ 使用备用category字段: ${productData.category}`);
                        return {
                            labelDesc: productData.category,
                            source: 'category_field_backup'
                        };
                    }

                    // 方法4: 默认类目
                    console.log('使用默认类目: 洗护清洁剂/卫生巾/纸/香薰');
                    return {
                        labelDesc: '洗护清洁剂/卫生巾/纸/香薰',
                        source: 'default'
                    };

                } catch (error) {
                    console.error('获取类目信息失败:', error);
                    return null;
                }
            },

            // 检查是否在类目选择页面
            isOnCategorySelectionPage() {
                console.log('🔍 检查是否在类目选择页面...');

                // 检查页面URL
                const url = window.location.href;
                if (url.includes('category') || url.includes('选择类目') || url.includes('classify')) {
                    console.log('✅ URL包含类目相关关键词');
                    return true;
                }

                // 检查页面标题
                const title = document.title;
                if (title.includes('类目') || title.includes('分类') || title.includes('Category')) {
                    console.log('✅ 页面标题包含类目相关关键词');
                    return true;
                }

                // 检查页面元素 - 更全面的选择器
                const categorySelectors = [
                    // 类目选择器
                    '.category-selector',
                    '.category-select',
                    '.category-picker',

                    // 类目搜索
                    '.category-search',
                    'input[placeholder*="搜索类目"]',
                    'input[placeholder*="请输入类目"]',
                    'input[placeholder*="输入类目名称"]',
                    'input[placeholder*="类目搜索"]',

                    // 类目树和列表
                    '.category-tree',
                    '.category-list',
                    '.category-menu',
                    '.category-panel',

                    // 抖音小店特定选择器
                    '.goods-category',
                    '.product-category',
                    '.ecom-category',

                    // 通用类目相关
                    '[data-testid*="category"]',
                    '[class*="category"]',
                    '[id*="category"]',

                    // 文本内容检查
                    'div:contains("选择类目")',
                    'div:contains("商品类目")',
                    'div:contains("请选择类目")'
                ];

                for (const selector of categorySelectors) {
                    try {
                        if (selector.includes(':contains')) {
                            // 处理包含文本的选择器
                            const text = selector.match(/:contains\("(.+)"\)/)[1];
                            const elements = document.querySelectorAll('div, span, label, button');
                            for (const element of elements) {
                                if (element.textContent.includes(text) && element.offsetWidth > 0) {
                                    console.log(`✅ 找到类目相关元素: ${text}`);
                                    return true;
                                }
                            }
                        } else {
                            const element = document.querySelector(selector);
                            if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
                                console.log(`✅ 找到类目选择元素: ${selector}`);
                                return true;
                            }
                        }
                    } catch (e) {
                        // 忽略选择器错误
                    }
                }

                // 检查是否有类目相关的文本内容
                const bodyText = document.body.textContent || '';
                const categoryKeywords = ['选择类目', '商品类目', '请选择类目', '类目搜索', '分类选择'];
                for (const keyword of categoryKeywords) {
                    if (bodyText.includes(keyword)) {
                        console.log(`✅ 页面内容包含类目关键词: ${keyword}`);
                        return true;
                    }
                }

                console.log('❌ 未检测到类目选择页面特征');
                return false;
            },

            // 选择无品牌
            async selectNoBrand() {
                try {
                    console.log('🏷️ 选择无品牌...');

                    // 查找无品牌选项的多种可能选择器
                    const noBrandSelectors = [
                        'input[value="无品牌"]',
                        'input[type="radio"][value="无品牌"]',
                        'label:contains("无品牌")',
                        '.radio-item:contains("无品牌")',
                        '[data-value="无品牌"]',
                        'span:contains("无品牌")',
                        '.brand-option:contains("无品牌")'
                    ];

                    for (const selector of noBrandSelectors) {
                        try {
                            const element = document.querySelector(selector);
                            if (element && element.offsetParent !== null) {
                                console.log(`✅ 找到无品牌选项: ${selector}`);
                                element.click();
                                await this.sleep(500);
                                return true;
                            }
                        } catch (e) {
                            // 继续尝试下一个选择器
                        }
                    }

                    // 如果直接选择器找不到，尝试通过文本查找
                    const allElements = document.querySelectorAll('*');
                    for (const element of allElements) {
                        if (element.textContent && element.textContent.trim() === '无品牌') {
                            console.log('✅ 通过文本找到无品牌选项');
                            element.click();
                            await this.sleep(500);
                            return true;
                        }
                    }

                    console.warn('⚠️ 未找到无品牌选项');
                    return false;

                } catch (error) {
                    console.error('❌ 选择无品牌失败:', error);
                    return false;
                }
            },

            // 填写产品名称
            async fillProductName(productName) {
                try {
                    console.log('📝 填写产品名称:', productName);

                    // 查找产品名称输入框的多种可能选择器
                    const nameInputSelectors = [
                        // 基于您最新提供的HTML结构
                        'input.ecom-g-input[dropdownclassname="auto-dropdown-id-产品名称"]',

                        // 基于之前的精确HTML结构
                        'input[id="rc_select_1"]',
                        '.ecom-g-select-selection-search input.ecom-g-select-selection-search-input',
                        'span.ecom-g-select-selection-search input',

                        // 基于您提供的具体选择器
                        'input.ecom-g-select-selection-search-input[role="combobox"]',
                        'input[id^="rc_select_"][type="search"]',
                        'input[aria-autocomplete="list"]',
                        'input[autocomplete="off"][type="search"]',

                        // 通用选择器
                        'input[placeholder*="产品名称"]',
                        'input[placeholder*="商品名称"]',
                        'input[name*="name"]',
                        'input[name*="productName"]',
                        'input[name*="itemName"]',
                        '.product-name input',
                        '.item-name input',
                        'input[type="search"]',
                        'input[type="text"]'
                    ];

                    for (const selector of nameInputSelectors) {
                        try {
                            const input = document.querySelector(selector);
                            if (input && input.offsetParent !== null) {
                                console.log(`✅ 找到产品名称输入框: ${selector}`);
                                console.log(`当前输入框值: "${input.value}"`);
                                console.log(`输入框ID: ${input.id}`);
                                console.log(`输入框类名: ${input.className}`);

                                // 对于搜索类型的输入框，需要特殊处理
                                input.focus();
                                await this.sleep(200);

                                // 清空现有内容（使用多种方法确保清空）
                                input.value = '';
                                input.setAttribute('value', '');

                                // 触发清空事件
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                                input.dispatchEvent(new Event('change', { bubbles: true }));

                                await this.sleep(300);

                                // 设置新值
                                input.value = productName;
                                input.setAttribute('value', productName);

                                // 触发输入事件（对于React搜索框很重要）
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                                input.dispatchEvent(new Event('change', { bubbles: true }));

                                // 触发键盘事件以确保React组件更新
                                input.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true, key: 'Enter' }));
                                input.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true, key: 'Enter' }));

                                // 模拟用户输入完成
                                input.dispatchEvent(new Event('blur', { bubbles: true }));

                                await this.sleep(500);

                                console.log(`✅ 产品名称填写完成: ${productName}`);
                                console.log(`验证填写结果: "${input.value}"`);
                                return true;
                            }
                        } catch (e) {
                            // 继续尝试下一个选择器
                        }
                    }

                    console.warn('⚠️ 未找到产品名称输入框');
                    return false;

                } catch (error) {
                    console.error('❌ 填写产品名称失败:', error);
                    return false;
                }
            },

            // 填写企业名称
            async fillCompanyName(companyName) {
                try {
                    console.log(`📝 填写企业名称: ${companyName}`);

                    // 查找企业名称输入框
                    const companyInputSelectors = [
                        'input.ecom-g-input[dropdownclassname="auto-dropdown-id-生产企业名称"]',
                        'input[placeholder*="企业名称"]',
                        'input[placeholder*="生产企业"]',
                        'input[placeholder*="公司名称"]'
                    ];

                    for (const selector of companyInputSelectors) {
                        try {
                            const input = document.querySelector(selector);
                            if (input && input.offsetParent !== null) {
                                console.log(`✅ 找到企业名称输入框: ${selector}`);

                                // 填写企业名称
                                input.focus();
                                await this.sleep(200);

                                input.value = '';
                                input.value = companyName;

                                // 触发输入事件
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                                input.dispatchEvent(new Event('blur', { bubbles: true }));

                                console.log(`✅ 企业名称填写完成: ${companyName}`);
                                return true;
                            }
                        } catch (e) {
                            // 继续尝试下一个选择器
                        }
                    }

                    console.warn('⚠️ 未找到企业名称输入框');
                    return false;

                } catch (error) {
                    console.error('❌ 填写企业名称失败:', error);
                    return false;
                }
            },

            // 获取元素的屏幕坐标
            getElementCoordinates(element) {
                try {
                    const rect = element.getBoundingClientRect();
                    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    return {
                        x: rect.left + scrollLeft + rect.width / 2,  // 元素中心X坐标
                        y: rect.top + scrollTop + rect.height / 2,   // 元素中心Y坐标
                        rect: rect,
                        visible: rect.width > 0 && rect.height > 0
                    };
                } catch (error) {
                    console.error('❌ 获取元素坐标失败:', error);
                    return null;
                }
            },

            // 模拟真实鼠标点击（基于坐标）- 支持多次点击
            async simulateRealClick(element, description = '元素', maxAttempts = 3) {
                try {
                    console.log(`🖱️ 模拟真实鼠标点击: ${description} (最多${maxAttempts}次)`);

                    // 获取元素坐标
                    const coords = this.getElementCoordinates(element);
                    if (!coords || !coords.visible) {
                        console.warn(`⚠️ 无法获取${description}的有效坐标`);
                        return false;
                    }

                    console.log(`📍 ${description}坐标: (${Math.round(coords.x)}, ${Math.round(coords.y)})`);
                    console.log(`📏 ${description}尺寸: ${coords.rect.width}x${coords.rect.height}`);

                    // 确保元素在视口中可见
                    element.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
                    await this.sleep(300);

                    // 多次点击同一位置
                    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                        console.log(`🖱️ 第${attempt}次点击${description}...`);

                        // 重新获取坐标（防止页面滚动后坐标变化）
                        const currentCoords = this.getElementCoordinates(element);
                        if (!currentCoords || !currentCoords.visible) {
                            console.warn(`⚠️ 第${attempt}次点击时无法获取有效坐标`);
                            continue;
                        }

                        // 创建鼠标事件序列
                        const mouseEvents = [
                            new MouseEvent('mouseover', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                                clientY: currentCoords.rect.top + currentCoords.rect.height / 2
                            }),
                            new MouseEvent('mousedown', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                button: 0,
                                buttons: 1,
                                clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                                clientY: currentCoords.rect.top + currentCoords.rect.height / 2
                            }),
                            new MouseEvent('mouseup', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                button: 0,
                                buttons: 0,
                                clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                                clientY: currentCoords.rect.top + currentCoords.rect.height / 2
                            }),
                            new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                button: 0,
                                buttons: 0,
                                clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                                clientY: currentCoords.rect.top + currentCoords.rect.height / 2,
                                detail: attempt  // 使用点击次数作为detail
                            })
                        ];

                        // 依次触发鼠标事件
                        for (const event of mouseEvents) {
                            element.dispatchEvent(event);
                            await this.sleep(30); // 缩短事件间隔
                        }

                        console.log(`✅ 第${attempt}次点击${description}完成`);

                        // 每次点击后等待一段时间
                        if (attempt < maxAttempts) {
                            await this.sleep(500); // 点击间隔
                        }
                    }

                    console.log(`✅ ${description}多次真实鼠标点击完成 (共${maxAttempts}次)`);
                    return true;

                } catch (error) {
                    console.error(`❌ 模拟真实鼠标点击失败:`, error);
                    return false;
                }
            },

            // 点击其他控件来分散注意力（触发页面状态更新）
            async clickOtherControlsToRefresh(currentElement, description = '当前元素') {
                try {
                    console.log(`🔄 点击其他控件来刷新页面状态 (当前: ${description})`);

                    // 查找页面上其他可点击的控件
                    const otherControlSelectors = [
                        'input[type="text"]:not([readonly]):not([disabled])',
                        'textarea:not([readonly]):not([disabled])',
                        'button:not([disabled])',
                        '.ecom-g-select:not(.ecom-g-select-disabled)',
                        'label',
                        '.form-item',
                        '.field-wrapper'
                    ];

                    let clickedControls = 0;
                    const maxClicks = 2; // 最多点击2个其他控件

                    for (const selector of otherControlSelectors) {
                        if (clickedControls >= maxClicks) break;

                        const controls = document.querySelectorAll(selector);
                        for (const control of controls) {
                            // 跳过当前元素和其父元素
                            if (control === currentElement ||
                                control.contains(currentElement) ||
                                currentElement.contains(control)) {
                                continue;
                            }

                            // 确保控件可见且可点击
                            if (control.offsetWidth > 0 && control.offsetHeight > 0) {
                                console.log(`🖱️ 点击其他控件: ${control.tagName}.${control.className || 'no-class'}`);

                                // 点击其他控件
                                control.click();
                                await this.sleep(200);

                                // 如果是输入框，失去焦点
                                if (control.tagName === 'INPUT' || control.tagName === 'TEXTAREA') {
                                    control.blur();
                                    await this.sleep(100);
                                }

                                clickedControls++;
                                if (clickedControls >= maxClicks) break;
                            }
                        }
                    }

                    console.log(`✅ 已点击 ${clickedControls} 个其他控件来刷新页面状态`);
                    return clickedControls > 0;

                } catch (error) {
                    console.error('❌ 点击其他控件失败:', error);
                    return false;
                }
            },

            // 分散注意力点击策略
            async distractedClickStrategy(element, description = '元素') {
                try {
                    console.log(`🎯 使用分散注意力点击策略: ${description}`);

                    // 第一步：初次点击目标元素
                    console.log(`🖱️ 第1步: 初次点击${description}...`);
                    const firstClick = await this.simulateRealClick(element, description, 1);
                    await this.sleep(300);

                    // 第二步：点击其他控件来分散注意力
                    console.log(`🔄 第2步: 点击其他控件来分散注意力...`);
                    await this.clickOtherControlsToRefresh(element, description);
                    await this.sleep(500);

                    // 第三步：回来再次点击目标元素
                    console.log(`🖱️ 第3步: 回来再次点击${description}...`);
                    const secondClick = await this.simulateRealClick(element, description, 2);
                    await this.sleep(300);

                    // 第四步：如果还没成功，再次分散注意力后点击
                    console.log(`🔄 第4步: 再次分散注意力...`);
                    await this.clickOtherControlsToRefresh(element, description);
                    await this.sleep(300);

                    console.log(`🖱️ 第5步: 最后一次点击${description}...`);
                    const thirdClick = await this.simulateRealClick(element, description, 1);

                    console.log(`✅ 分散注意力点击策略完成: ${description}`);
                    return firstClick || secondClick || thirdClick;

                } catch (error) {
                    console.error(`❌ 分散注意力点击策略失败:`, error);
                    return false;
                }
            },

            // 完全模拟真实用户操作的点击策略 - 循环2次
            async simulateRealUserClick(element, description = '元素', cycles = 2) {
                try {
                    console.log(`👤 完全模拟真实用户操作: ${description} (循环${cycles}次)`);

                    // 获取元素坐标
                    const coords = this.getElementCoordinates(element);
                    if (!coords || !coords.visible) {
                        console.warn(`⚠️ 无法获取${description}的有效坐标`);
                        return false;
                    }

                    console.log(`📍 ${description}坐标: (${Math.round(coords.x)}, ${Math.round(coords.y)})`);

                    // 确保元素在视口中可见
                    element.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
                    await this.sleep(500);

                    // 循环执行完整的用户操作序列
                    for (let cycle = 1; cycle <= cycles; cycle++) {
                        console.log(`🔄 第${cycle}轮完整用户操作序列...`);

                        // 重新获取坐标（防止页面变化）
                        const currentCoords = this.getElementCoordinates(element);
                        if (!currentCoords || !currentCoords.visible) {
                            console.warn(`⚠️ 第${cycle}轮无法获取有效坐标`);
                            continue;
                        }

                        // 第一步：鼠标移动到元素上（hover效果）
                        console.log(`🖱️ 第${cycle}轮-第1步: 鼠标移动到${description}上...`);
                        element.dispatchEvent(new MouseEvent('mouseenter', {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                            clientY: currentCoords.rect.top + currentCoords.rect.height / 2
                        }));

                        element.dispatchEvent(new MouseEvent('mouseover', {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                            clientY: currentCoords.rect.top + currentCoords.rect.height / 2
                        }));
                        await this.sleep(200);

                        // 第二步：获取焦点（如果是可聚焦元素）
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tabIndex >= 0) {
                            console.log(`👆 第${cycle}轮-第2步: 获取${description}焦点...`);
                            element.focus();

                            // 触发focusin事件
                            element.dispatchEvent(new FocusEvent('focusin', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            }));

                            element.dispatchEvent(new FocusEvent('focus', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            }));
                            await this.sleep(300);
                        }

                        // 第三步：鼠标按下
                        console.log(`🖱️ 第${cycle}轮-第3步: 鼠标按下${description}...`);
                        element.dispatchEvent(new MouseEvent('mousedown', {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            button: 0,
                            buttons: 1,
                            clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                            clientY: currentCoords.rect.top + currentCoords.rect.height / 2,
                            detail: cycle
                        }));
                        await this.sleep(100);

                        // 第四步：鼠标抬起
                        console.log(`🖱️ 第${cycle}轮-第4步: 鼠标抬起${description}...`);
                        element.dispatchEvent(new MouseEvent('mouseup', {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            button: 0,
                            buttons: 0,
                            clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                            clientY: currentCoords.rect.top + currentCoords.rect.height / 2,
                            detail: cycle
                        }));
                        await this.sleep(50);

                        // 第五步：点击事件
                        console.log(`🖱️ 第${cycle}轮-第5步: 点击${description}...`);
                        element.dispatchEvent(new MouseEvent('click', {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            button: 0,
                            buttons: 0,
                            clientX: currentCoords.rect.left + currentCoords.rect.width / 2,
                            clientY: currentCoords.rect.top + currentCoords.rect.height / 2,
                            detail: cycle
                        }));
                        await this.sleep(100);

                        // 第六步：如果是输入框，触发input和change事件
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                            console.log(`📝 第${cycle}轮-第6步: 触发${description}的input/change事件...`);
                            element.dispatchEvent(new Event('input', { bubbles: true }));
                            element.dispatchEvent(new Event('change', { bubbles: true }));
                            await this.sleep(100);
                        }

                        // 第七步：触发可能的自定义事件
                        console.log(`⚡ 第${cycle}轮-第7步: 触发${description}的自定义事件...`);

                        // 尝试触发一些常见的自定义事件
                        const customEvents = ['select', 'toggle', 'open', 'show', 'dropdown'];
                        for (const eventName of customEvents) {
                            try {
                                element.dispatchEvent(new CustomEvent(eventName, {
                                    bubbles: true,
                                    cancelable: true,
                                    detail: { synthetic: true, cycle: cycle }
                                }));
                            } catch (e) {
                                // 忽略自定义事件错误
                            }
                        }
                        await this.sleep(200);

                        console.log(`✅ 第${cycle}轮完整用户操作序列完成`);

                        // 每轮之间的间隔
                        if (cycle < cycles) {
                            console.log(`⏳ 等待第${cycle + 1}轮操作...`);
                            await this.sleep(800);
                        }
                    }

                    console.log(`✅ ${description}完全真实用户操作完成 (共${cycles}轮)`);
                    return true;

                } catch (error) {
                    console.error(`❌ 完全真实用户操作失败:`, error);
                    return false;
                }
            },

            // 通过API创建抽数名称
            async createSKUNameByAPI() {
                try {
                    console.log('📡 通过API创建抽数名称...');

                    // 构建请求数据
                    const requestData = {
                        "key": "property_value",
                        "value": "见商品图",
                        "product_type": "0",
                        "category_leaf_id": 40506, // 这个需要从当前页面获取
                        "context": {
                            "ability": [],
                            "biz_identity": "xiaodian",
                            "category_id": "40506", // 这个需要从当前页面获取
                            "feature": {
                                "not_first_render": "1",
                                "session_data": "{\"stock_incr_mode\":false,\"only_update_stock\":null}"
                            },
                            "model_type": "dpu",
                            "operation_type": "normal",
                            "product_id": "0",
                            "version": "v1_v8_v9"
                        },
                        "_bid": "ffa_goods"
                    };

                    // 发送POST请求
                    const response = await fetch('/fxg/product/tproduct/getBadWordHint', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json;charset=UTF-8',
                            'Accept': 'application/json, text/plain, */*',
                            'X-Ecom-Platform-Source': 'uscmSupplyDistritionDpu',
                            'X-TT-From-Appid': 'ffa-goods',
                            'X-TT-From-End': 'PC'
                        },
                        body: JSON.stringify(requestData)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log('✅ API创建抽数名称成功:', result);
                        return true;
                    } else {
                        console.warn('⚠️ API创建抽数名称失败:', response.status, response.statusText);
                        return false;
                    }

                } catch (error) {
                    console.error('❌ API创建抽数名称异常:', error);
                    return false;
                }
            },

            // 新建SKU（抽数选择）
            async createNewSKU() {
                try {
                    console.log('🆕 开始新建SKU（抽数选择）...');

                    // 第一步：发送POST请求创建抽数名称
                    console.log('📡 发送POST请求创建抽数名称...');
                    const createResult = await this.createSKUNameByAPI();
                    if (!createResult) {
                        console.warn('⚠️ API创建抽数名称失败，尝试手动方式...');
                    }

                    // 第二步：查找抽数选择框并获取焦点来触发数据加载
                    console.log('👆 查找抽数选择框并获取焦点来触发数据加载...');

                    const skuInputSelectors = [
                        'input#rc_select_1[aria-controls="rc_select_1_list"]',
                        'input#rc_select_1',
                        'div[attr-field-id="抽数"] input.ecom-g-select-selection-search-input',
                        'div[attr-field-id="抽数"] input[role="combobox"]',
                        'input[aria-controls="rc_select_1_list"]'
                    ];

                    let skuInput = null;
                    for (const selector of skuInputSelectors) {
                        skuInput = document.querySelector(selector);
                        if (skuInput && skuInput.offsetWidth > 0) {
                            console.log(`✅ 找到抽数选择框: ${selector}`);
                            break;
                        }
                    }

                    if (!skuInput) {
                        console.warn('⚠️ 未找到抽数选择框');
                        return false;
                    }

                    // 第三步：等待API请求完成后页面更新
                    if (createResult) {
                        console.log('⏳ 等待API创建完成后页面更新...');
                        await this.sleep(2000); // 等待页面更新
                    }

                    // 第四步：完整的抽数选择框操作序列
                    console.log('🖱️ 开始完整的抽数选择框操作序列...');

                    // 先分析页面DOM结构
                    console.log('🔍 分析抽数选择区域的DOM结构...');
                    const skuArea = document.querySelector('div[attr-field-id="抽数"]');
                    if (skuArea) {
                        console.log('📋 抽数区域HTML结构:', skuArea.outerHTML.substring(0, 500) + '...');

                        // 查找所有可能的点击元素
                        const allClickableElements = skuArea.querySelectorAll('*');
                        console.log(`📋 抽数区域内共有 ${allClickableElements.length} 个元素`);

                        allClickableElements.forEach((el, index) => {
                            if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                                console.log(`📋 可见元素${index + 1}: ${el.tagName}.${el.className} - 尺寸: ${el.offsetWidth}x${el.offsetHeight}`);
                            }
                        });
                    }

                    // 查找抽数选择框容器和下拉箭头
                    const selectContainerSelectors = [
                        'div[attr-field-id="抽数"] .ecom-g-select-selector',
                        'div[attr-field-id="抽数"] .ecom-g-select',
                        '.ecom-g-select-selector',
                        '.ecom-g-select'
                    ];

                    const arrowSelectors = [
                        'div[attr-field-id="抽数"] .ecom-g-select-arrow',
                        'div[attr-field-id="抽数"] .anticon-down',
                        'div[attr-field-id="抽数"] .ecom-g-select-suffix',
                        '.ecom-g-select-arrow',
                        '.anticon-down',
                        '.ecom-g-select-suffix'
                    ];

                    let selectContainer = null;
                    let arrowElement = null;

                    // 查找选择框容器
                    for (const selector of selectContainerSelectors) {
                        selectContainer = document.querySelector(selector);
                        if (selectContainer && selectContainer.offsetWidth > 0) {
                            console.log(`✅ 找到抽数选择框容器: ${selector}`);
                            break;
                        }
                    }

                    // 查找下拉箭头
                    for (const selector of arrowSelectors) {
                        arrowElement = document.querySelector(selector);
                        if (arrowElement && arrowElement.offsetWidth > 0) {
                            console.log(`✅ 找到抽数下拉箭头: ${selector}`);
                            break;
                        }
                    }

                    // 多次尝试完整的操作序列
                    const maxAttempts = 3;
                    let hasDropdownOptions = false;

                    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                        console.log(`🔄 第${attempt}次尝试抽数选择框操作序列...`);

                        // 步骤1：获取输入框焦点
                        console.log(`👆 步骤1: 获取抽数输入框焦点...`);
                        skuInput.focus();
                        await this.sleep(800);

                        // 步骤2：点击选择框容器
                        if (selectContainer) {
                            console.log(`🖱️ 步骤2: 点击抽数选择框容器...`);
                            selectContainer.click();
                            await this.sleep(800);
                        }

                        // 步骤3：使用分散注意力策略点击下拉箭头
                        if (arrowElement) {
                            console.log(`🔽 步骤3: 使用分散注意力策略点击抽数下拉箭头...`);
                            console.log(`🔍 调试: 抽数箭头元素信息:`, {
                                tagName: arrowElement.tagName,
                                className: arrowElement.className,
                                offsetWidth: arrowElement.offsetWidth,
                                offsetHeight: arrowElement.offsetHeight,
                                style: arrowElement.style.cssText,
                                visible: arrowElement.offsetWidth > 0 && arrowElement.offsetHeight > 0,
                                disabled: arrowElement.disabled,
                                readonly: arrowElement.readOnly
                            });

                            // 使用完全真实用户操作
                            console.log(`🎯 尝试完全真实用户操作...`);
                            const realUserClick = await this.simulateRealUserClick(arrowElement, '抽数下拉箭头');

                            if (!realUserClick) {
                                console.log(`⚠️ 真实用户操作失败，尝试分散注意力策略...`);
                                const distractedClick = await this.distractedClickStrategy(arrowElement, '抽数下拉箭头');

                                if (!distractedClick) {
                                    console.log(`⚠️ 分散注意力策略失败，尝试传统多次点击...`);
                                    // 传统方式也尝试多次点击
                                    for (let i = 1; i <= 3; i++) {
                                        console.log(`🖱️ 传统方式第${i}次点击抽数箭头...`);
                                        arrowElement.click();
                                        arrowElement.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
                                        await this.sleep(300);
                                    }
                                }
                            }

                            await this.sleep(1000);
                        } else {
                            // 如果没找到箭头，尝试点击输入框
                            console.log(`🔽 步骤3: 未找到箭头，使用分散注意力策略点击输入框...`);
                            console.log(`🔍 调试: 抽数输入框元素信息:`, {
                                tagName: skuInput.tagName,
                                className: skuInput.className,
                                offsetWidth: skuInput.offsetWidth,
                                offsetHeight: skuInput.offsetHeight,
                                disabled: skuInput.disabled,
                                readonly: skuInput.readOnly
                            });

                            const clickSuccess = await this.distractedClickStrategy(skuInput, '抽数输入框');
                            if (!clickSuccess) {
                                skuInput.click();
                            }

                            await this.sleep(1000);
                        }

                        // 步骤4：检查是否有下拉选项出现
                        console.log(`🔍 步骤4: 检查下拉选项...`);
                        const dropdownOptions = document.querySelectorAll('.ecom-g-select-item, .ecom-g-select-item-option');
                        const visibleOptions = Array.from(dropdownOptions).filter(option =>
                            option.offsetWidth > 0 && option.offsetHeight > 0
                        );

                        if (visibleOptions.length > 0) {
                            console.log(`✅ 第${attempt}次操作序列成功，检测到 ${visibleOptions.length} 个抽数下拉选项`);
                            hasDropdownOptions = true;
                            break;
                        } else {
                            console.log(`⚠️ 第${attempt}次操作序列后未检测到下拉选项，继续尝试...`);
                            // 等待一下再进行下次尝试
                            await this.sleep(1000);
                        }
                    }

                    if (!hasDropdownOptions) {
                        console.warn('⚠️ 多次操作序列后仍未检测到抽数下拉选项');
                    }

                    // 第五步：等待下拉框展开并查找选项
                    console.log('⏳ 等待抽数下拉框完全展开和数据加载...');
                    await this.sleep(2000); // 等待下拉选项完全加载

                    // 第七步：查找并选择抽数选项（优先选择"见商品图"）
                    console.log('🔍 查找抽数选项...');

                    // 先调试查看页面上的下拉选项
                    console.log('🔍 调试：查看页面上的下拉选项...');
                    const allSelectItems = document.querySelectorAll('.ecom-g-select-item, .ecom-g-select-item-option, [class*="select-item"], [class*="option"]');
                    console.log(`📋 找到 ${allSelectItems.length} 个可能的选项元素`);
                    allSelectItems.forEach((item, index) => {
                        if (item.offsetWidth > 0 && item.offsetHeight > 0) {
                            console.log(`📋 选项${index + 1}: ${item.tagName}.${item.className} - 文本: "${item.textContent.trim()}"`);
                        }
                    });

                    const optionSelectors = [
                        '.ecom-g-select-item:contains("见商品图")',
                        '.ecom-g-select-item-option:contains("见商品图")',
                        '.ecom-g-select-item-option-content:contains("见商品图")',
                        '[title="见商品图"]',
                        '.ecom-g-select-item',
                        '.ecom-g-select-item-option',
                        '.ecom-g-select-item:first-child',
                        '.ecom-g-select-item-option:first-child'
                    ];

                    for (const selector of optionSelectors) {
                        if (selector.includes(':contains(')) {
                            // 处理包含文本的选择器
                            const text = selector.match(/:contains\("([^"]+)"\)/)[1];
                            const elements = document.querySelectorAll('*');
                            for (const element of elements) {
                                if (element.textContent.trim() === text &&
                                    element.offsetWidth > 0 &&
                                    element.offsetHeight > 0 &&
                                    element.style.display !== 'none') {
                                    console.log(`✅ 找到抽数选项: ${element.tagName}.${element.className} (文本: ${element.textContent.trim()})`);

                                    // 滚动到选项位置
                                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    await this.sleep(200);

                                    // 使用多种点击方式
                                    element.click();
                                    element.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));

                                    await this.sleep(300);
                                    console.log('✅ 已选择抽数选项');
                                    return true;
                                }
                            }
                        } else {
                            const option = document.querySelector(selector);
                            if (option && option.offsetWidth > 0) {
                                console.log(`✅ 找到抽数选项: ${selector}`);
                                option.click();
                                await this.sleep(300);
                                console.log('✅ 已选择抽数选项');
                                return true;
                            }
                        }
                    }

                    console.warn('⚠️ 未找到抽数选项，尝试手动输入...');

                    // 如果没有找到选项，尝试直接在输入框中输入
                    skuInput.focus();
                    skuInput.value = '见商品图';
                    skuInput.dispatchEvent(new Event('input', { bubbles: true }));
                    skuInput.dispatchEvent(new Event('change', { bubbles: true }));
                    skuInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));

                    console.log('✅ 抽数内容手动输入完成: 见商品图');
                    return true;

                } catch (error) {
                    console.error('❌ 新建SKU失败:', error);
                    return false;
                }
            },

            // 选择功效（清洁去污）
            async selectCleaningEffect() {
                try {
                    console.log('🧽 选择功效: 清洁去污...');

                    // 检查是否已经选择了"清洁去污"
                    const selectedEffect = document.querySelector('span.ecom-g-select-selection-item-content');
                    if (selectedEffect && selectedEffect.textContent.includes('清洁去污')) {
                        console.log('✅ 已选择"清洁去污"功效');
                        return true;
                    }

                    // 查找功效选择输入框（需要先获取焦点来触发下拉选项加载）
                    const inputSelectors = [
                        'input#rc_select_0[aria-controls="rc_select_0_list"]',
                        'input#rc_select_0',
                        'input.ecom-g-select-selection-search-input[role="combobox"]',
                        'input[aria-controls="rc_select_0_list"]',
                        'input[aria-controls*="rc_select"][role="combobox"]',
                        'input[aria-expanded]',
                        '.ecom-g-select-selection-search input'
                    ];

                    let inputElement = null;
                    for (const selector of inputSelectors) {
                        inputElement = document.querySelector(selector);
                        if (inputElement && inputElement.offsetWidth > 0) {
                            console.log(`✅ 找到功效选择输入框: ${selector}`);
                            break;
                        }
                    }

                    if (!inputElement) {
                        console.warn('⚠️ 未找到功效选择输入框');
                        return false;
                    }

                    // 第一步：完整的功效选择框操作序列
                    console.log('🖱️ 开始完整的功效选择框操作序列...');

                    // 先分析页面DOM结构
                    console.log('🔍 分析功效选择区域的DOM结构...');
                    const effectArea = document.querySelector('div[attr-field-id="功效"]');
                    if (effectArea) {
                        console.log('📋 功效区域HTML结构:', effectArea.outerHTML.substring(0, 500) + '...');

                        // 查找所有可能的点击元素
                        const allClickableElements = effectArea.querySelectorAll('*');
                        console.log(`📋 功效区域内共有 ${allClickableElements.length} 个元素`);

                        allClickableElements.forEach((el, index) => {
                            if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                                console.log(`📋 可见元素${index + 1}: ${el.tagName}.${el.className} - 尺寸: ${el.offsetWidth}x${el.offsetHeight}`);
                            }
                        });
                    }

                    // 查找功效选择框容器和下拉箭头
                    const selectContainerSelectors = [
                        'div[attr-field-id="功效"] .ecom-g-select-selector',
                        'div[attr-field-id="功效"] .ecom-g-select',
                        '.ecom-g-select-selector',
                        '.ecom-g-select'
                    ];

                    const arrowSelectors = [
                        'div[attr-field-id="功效"] .ecom-g-select-arrow',
                        'div[attr-field-id="功效"] .anticon-down',
                        'div[attr-field-id="功效"] .ecom-g-select-suffix',
                        '.ecom-g-select-arrow',
                        '.anticon-down',
                        '.ecom-g-select-suffix'
                    ];

                    let selectContainer = null;
                    let arrowElement = null;

                    // 查找选择框容器
                    for (const selector of selectContainerSelectors) {
                        selectContainer = document.querySelector(selector);
                        if (selectContainer && selectContainer.offsetWidth > 0) {
                            console.log(`✅ 找到功效选择框容器: ${selector}`);
                            break;
                        }
                    }

                    // 查找下拉箭头
                    for (const selector of arrowSelectors) {
                        arrowElement = document.querySelector(selector);
                        if (arrowElement && arrowElement.offsetWidth > 0) {
                            console.log(`✅ 找到功效下拉箭头: ${selector}`);
                            break;
                        }
                    }

                    // 多次尝试完整的操作序列
                    const maxAttempts = 3;
                    let hasDropdownOptions = false;

                    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                        console.log(`🔄 第${attempt}次尝试功效选择框操作序列...`);

                        // 步骤1：获取输入框焦点
                        console.log(`👆 步骤1: 获取功效输入框焦点...`);
                        inputElement.focus();
                        await this.sleep(800);

                        // 步骤2：点击选择框容器
                        if (selectContainer) {
                            console.log(`🖱️ 步骤2: 点击功效选择框容器...`);
                            selectContainer.click();
                            await this.sleep(800);
                        }

                        // 步骤3：使用分散注意力策略点击下拉箭头
                        if (arrowElement) {
                            console.log(`🔽 步骤3: 使用分散注意力策略点击功效下拉箭头...`);
                            console.log(`🔍 调试: 功效箭头元素信息:`, {
                                tagName: arrowElement.tagName,
                                className: arrowElement.className,
                                offsetWidth: arrowElement.offsetWidth,
                                offsetHeight: arrowElement.offsetHeight,
                                style: arrowElement.style.cssText,
                                visible: arrowElement.offsetWidth > 0 && arrowElement.offsetHeight > 0,
                                disabled: arrowElement.disabled,
                                readonly: arrowElement.readOnly
                            });

                            // 使用完全真实用户操作
                            console.log(`🎯 尝试完全真实用户操作...`);
                            const realUserClick = await this.simulateRealUserClick(arrowElement, '功效下拉箭头');

                            if (!realUserClick) {
                                console.log(`⚠️ 真实用户操作失败，尝试分散注意力策略...`);
                                const distractedClick = await this.distractedClickStrategy(arrowElement, '功效下拉箭头');

                                if (!distractedClick) {
                                    console.log(`⚠️ 分散注意力策略失败，尝试传统多次点击...`);
                                    // 传统方式也尝试多次点击
                                    for (let i = 1; i <= 3; i++) {
                                        console.log(`🖱️ 传统方式第${i}次点击功效箭头...`);
                                        arrowElement.click();
                                        arrowElement.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
                                        await this.sleep(300);
                                    }
                                }
                            }

                            await this.sleep(1000);
                        } else {
                            // 如果没找到箭头，尝试点击输入框
                            console.log(`🔽 步骤3: 未找到箭头，使用分散注意力策略点击输入框...`);
                            console.log(`🔍 调试: 功效输入框元素信息:`, {
                                tagName: inputElement.tagName,
                                className: inputElement.className,
                                offsetWidth: inputElement.offsetWidth,
                                offsetHeight: inputElement.offsetHeight,
                                disabled: inputElement.disabled,
                                readonly: inputElement.readOnly
                            });

                            const clickSuccess = await this.distractedClickStrategy(inputElement, '功效输入框');
                            if (!clickSuccess) {
                                inputElement.click();
                            }

                            await this.sleep(1000);
                        }

                        // 步骤4：检查是否有下拉选项出现
                        console.log(`🔍 步骤4: 检查下拉选项...`);
                        const dropdownOptions = document.querySelectorAll('.ecom-g-select-item, .ecom-g-select-item-option, .ecom-g-tree-select-node');
                        const visibleOptions = Array.from(dropdownOptions).filter(option =>
                            option.offsetWidth > 0 && option.offsetHeight > 0
                        );

                        if (visibleOptions.length > 0) {
                            console.log(`✅ 第${attempt}次操作序列成功，检测到 ${visibleOptions.length} 个功效下拉选项`);
                            hasDropdownOptions = true;
                            break;
                        } else {
                            console.log(`⚠️ 第${attempt}次操作序列后未检测到下拉选项，继续尝试...`);
                            // 等待一下再进行下次尝试
                            await this.sleep(1000);
                        }
                    }

                    if (!hasDropdownOptions) {
                        console.warn('⚠️ 多次操作序列后仍未检测到功效下拉选项');
                    }

                    // 第二步：等待下拉框完全展开和数据加载
                    console.log('⏳ 等待功效下拉框完全展开和数据加载...');
                    await this.sleep(2000); // 等待下拉选项完全加载

                    // 第四步：查找"清洁去污"选项
                    console.log('🔍 查找"清洁去污"选项...');

                    // 先调试查看页面上的下拉选项
                    console.log('🔍 调试：查看页面上的功效下拉选项...');
                    const allSelectItems = document.querySelectorAll('.ecom-g-select-item, .ecom-g-select-item-option, [class*="select-item"], [class*="option"], .ecom-g-tree-select-node');
                    console.log(`📋 找到 ${allSelectItems.length} 个可能的功效选项元素`);
                    allSelectItems.forEach((item, index) => {
                        if (item.offsetWidth > 0 && item.offsetHeight > 0) {
                            console.log(`📋 功效选项${index + 1}: ${item.tagName}.${item.className} - 文本: "${item.textContent.trim()}"`);
                        }
                    });

                    const optionSelectors = [
                        '.ecom-g-select-item:contains("清洁去污")',
                        '.ecom-g-select-item-option:contains("清洁去污")',
                        '.ecom-g-select-item-option-content:contains("清洁去污")',
                        '.ecom-g-tree-select-node:contains("清洁去污")',
                        '[title="清洁去污"]',
                        '.ecom-g-select-item',
                        '.ecom-g-select-item-option',
                        '.ecom-g-tree-select-node',
                        '*:contains("清洁去污")'
                    ];

                    for (const selector of optionSelectors) {
                        if (selector.includes(':contains(')) {
                            // 处理包含文本的选择器
                            const text = selector.match(/:contains\("([^"]+)"\)/)[1];
                            const elements = document.querySelectorAll('*');
                            for (const element of elements) {
                                if (element.textContent.trim() === text &&
                                    element.offsetWidth > 0 &&
                                    element.offsetHeight > 0 &&
                                    element.style.display !== 'none') {
                                    console.log(`✅ 找到"清洁去污"选项: ${element.tagName}.${element.className}`);

                                    // 滚动到选项位置
                                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    await this.sleep(200);

                                    // 使用多种点击方式
                                    element.click();
                                    element.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
                                    element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true, cancelable: true }));
                                    element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true, cancelable: true }));

                                    await this.sleep(300);
                                    console.log('✅ 已点击"清洁去污"选项');
                                    return true;
                                }
                            }
                        } else {
                            const option = document.querySelector(selector);
                            if (option && option.offsetWidth > 0) {
                                console.log(`✅ 找到"清洁去污"选项: ${selector}`);
                                option.click();
                                await this.sleep(300);
                                return true;
                            }
                        }
                    }

                    console.warn('⚠️ 未找到"清洁去污"选项');
                    return false;

                } catch (error) {
                    console.error('❌ 选择功效失败:', error);
                    return false;
                }
            },

            // 填写包装规格
            async fillPackagingSpec() {
                try {
                    console.log('📦 填写包装规格...');

                    // 查找包装规格输入框
                    const specInputSelectors = [
                        'input.ecom-g-input[dropdownclassname="auto-dropdown-id-包装规格"]',
                        'input[placeholder*="包装规格"]',
                        'input[placeholder*="规格"]'
                    ];

                    for (const selector of specInputSelectors) {
                        try {
                            const input = document.querySelector(selector);
                            if (input && input.offsetParent !== null) {
                                console.log(`✅ 找到包装规格输入框: ${selector}`);

                                // 填写包装规格
                                input.focus();
                                await this.sleep(200);

                                input.value = '';
                                input.value = '见商品图';

                                // 触发输入事件
                                input.dispatchEvent(new Event('input', { bubbles: true }));
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                                input.dispatchEvent(new Event('blur', { bubbles: true }));

                                console.log('✅ 包装规格填写完成: 见商品图');
                                return true;
                            }
                        } catch (e) {
                            // 继续尝试下一个选择器
                        }
                    }

                    console.warn('⚠️ 未找到包装规格输入框');
                    return false;

                } catch (error) {
                    console.error('❌ 填写包装规格失败:', error);
                    return false;
                }
            },

            // 点击"从主图填入"
            async clickFillFromMainImage() {
                try {
                    console.log('🖼️ 点击"从主图填入"...');

                    // 查找"从主图填入"链接的多种选择器
                    const fillLinkSelectors = [
                        'a[href*="从主图填入"]',
                        'button:contains("从主图填入")',
                        'span:contains("从主图填入")',
                        'div:contains("从主图填入")',
                        'a:contains("从主图填入")',
                        '*:contains("从主图填入")',
                        '[title*="从主图填入"]',
                        '[aria-label*="从主图填入"]'
                    ];

                    for (const selector of fillLinkSelectors) {
                        try {
                            let link = null;
                            if (selector.includes(':contains(')) {
                                // 处理包含文本的选择器
                                const text = selector.match(/:contains\("([^"]+)"\)/)[1];
                                const elements = document.querySelectorAll('a, span, div, button');
                                for (const element of elements) {
                                    if (element.textContent.includes(text) &&
                                        element.offsetWidth > 0 &&
                                        element.offsetHeight > 0) {
                                        link = element;
                                        console.log(`✅ 找到"从主图填入"元素: ${element.tagName}.${element.className}`);
                                        break;
                                    }
                                }
                            } else {
                                link = document.querySelector(selector);
                                if (link) {
                                    console.log(`✅ 找到"从主图填入"链接: ${selector}`);
                                }
                            }

                            if (link && link.offsetWidth > 0) {
                                // 滚动到元素位置
                                link.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                await this.sleep(300);

                                // 尝试多种点击方式
                                console.log('🖱️ 尝试多种点击方式...');

                                // 方式1: 普通点击
                                link.click();
                                await this.sleep(200);

                                // 方式2: 模拟鼠标事件
                                link.dispatchEvent(new MouseEvent('mousedown', { bubbles: true, cancelable: true }));
                                link.dispatchEvent(new MouseEvent('mouseup', { bubbles: true, cancelable: true }));
                                link.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
                                await this.sleep(200);

                                // 方式3: 如果是链接，尝试触发href
                                if (link.tagName === 'A' && link.href) {
                                    console.log('🔗 尝试触发链接href...');
                                    window.location.href = link.href;
                                }

                                // 方式4: 触发焦点事件
                                link.focus();
                                link.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
                                link.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }));

                                await this.sleep(500);
                                console.log('✅ 已尝试点击"从主图填入"');
                                return true;
                            }
                        } catch (e) {
                            console.warn(`⚠️ 选择器 ${selector} 失败:`, e.message);
                            // 继续尝试下一个选择器
                        }
                    }

                    console.warn('⚠️ 未找到"从主图填入"链接');
                    return false;

                } catch (error) {
                    console.error('❌ 点击"从主图填入"失败:', error);
                    return false;
                }
            },

            // 检查是否在品牌和产品名称填写页面
            async isOnBrandAndProductPage() {
                try {
                    console.log('🔍 检查是否在品牌和产品名称页面...');

                    // 检查页面URL
                    const url = window.location.href;
                    console.log(`📋 当前页面URL: ${url}`);

                    // 检查页面标题
                    const pageTitle = document.title;
                    console.log(`📋 页面标题: ${pageTitle}`);

                    // 检查页面是否包含品牌和产品名称相关元素
                    let hasBrandSection = false;
                    let hasProductNameSection = false;
                    let hasNoBrandOption = false;

                    // 更精确的关键词检测
                    const brandKeywords = ['品牌', '无品牌', '选择品牌', '品牌信息', '品牌选择'];
                    const productNameKeywords = ['产品名称', '商品名称', '商品标题', '产品标题', '产品信息'];

                    // 检查可见元素
                    const visibleElements = Array.from(document.querySelectorAll('*')).filter(el =>
                        el.offsetWidth > 0 && el.offsetHeight > 0
                    );

                    console.log(`📋 页面可见元素数量: ${visibleElements.length}`);

                    for (const element of visibleElements) {
                        const text = element.textContent || '';

                        // 检查品牌相关
                        for (const keyword of brandKeywords) {
                            if (text.includes(keyword)) {
                                hasBrandSection = true;
                                console.log(`✅ 找到品牌相关元素: "${keyword}" - ${element.tagName}`);
                                break;
                            }
                        }

                        // 检查产品名称相关
                        for (const keyword of productNameKeywords) {
                            if (text.includes(keyword)) {
                                hasProductNameSection = true;
                                console.log(`✅ 找到产品名称相关元素: "${keyword}" - ${element.tagName}`);
                                break;
                            }
                        }

                        // 特别检查"无品牌"选项
                        if (text.includes('无品牌')) {
                            hasNoBrandOption = true;
                            console.log(`✅ 找到"无品牌"选项: ${element.tagName}`);
                        }
                    }

                    // 检查特定的输入框
                    const productNameInputs = document.querySelectorAll('input[type="search"], input[placeholder*="产品"], input[placeholder*="商品"]');
                    if (productNameInputs.length > 0) {
                        hasProductNameSection = true;
                        console.log(`✅ 找到产品名称输入框: ${productNameInputs.length} 个`);
                    }

                    const isOnPage = hasBrandSection || hasProductNameSection || hasNoBrandOption;
                    console.log(`🔍 品牌和产品名称页面检查结果:`);
                    console.log(`  - 品牌相关: ${hasBrandSection}`);
                    console.log(`  - 产品名称相关: ${hasProductNameSection}`);
                    console.log(`  - 无品牌选项: ${hasNoBrandOption}`);
                    console.log(`  - 最终结果: ${isOnPage ? '是' : '否'}`);

                    return isOnPage;

                } catch (error) {
                    console.error('❌ 检查品牌和产品名称页面失败:', error);
                    return false;
                }
            },

            // 等待品牌和产品名称页面加载
            async waitForBrandAndProductPage(maxWaitTime = 10000) {
                console.log('⏳ 等待品牌和产品名称页面加载...');
                const startTime = Date.now();

                while (Date.now() - startTime < maxWaitTime) {
                    if (await this.isOnBrandAndProductPage()) {
                        console.log('✅ 品牌和产品名称页面已加载');
                        return true;
                    }
                    await this.sleep(500);
                }

                console.warn('⚠️ 等待品牌和产品名称页面超时');
                return false;
            },

            // 处理品牌和产品名称页面
            async handleBrandAndProductPage(productData) {
                try {
                    console.log('🏷️ 自动处理品牌和产品名称页面...');
                    console.log(`📋 产品信息: ${productData.name}`);

                    // 等待页面加载
                    console.log('⏳ 等待品牌和产品名称页面完全加载...');
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 1. 选择无品牌
                    console.log('🏷️ 自动选择无品牌...');
                    const brandSelected = await this.selectNoBrand();
                    if (brandSelected) {
                        console.log('✅ 无品牌选择成功');
                    } else {
                        console.warn('⚠️ 无品牌选择失败，但继续处理');
                    }

                    await this.sleep(1000);

                    // 2. 填写产品名称
                    console.log('📝 自动填写产品名称...');
                    const nameResult = await this.fillProductName(productData.name);
                    if (nameResult) {
                        console.log('✅ 产品名称填写成功');
                    } else {
                        console.warn('⚠️ 产品名称填写失败，但继续处理');
                    }

                    await this.sleep(1000);

                    // 3. 填写企业名称（从缓存读取）
                    console.log('🏢 自动填写企业名称...');

                    // 从多个可能的字段读取企业名称，优先使用 supplier_name
                    let companyName = productData.supplier_name ||      // 优先级1：供应商名称
                                     productData.supplierName ||       // 优先级2：供应商名称（驼峰）
                                     productData.company_name ||       // 优先级3：公司名称
                                     productData.companyName ||        // 优先级4：公司名称（驼峰）
                                     productData.enterprise_name ||    // 优先级5：企业名称
                                     productData.enterpriseName ||     // 优先级6：企业名称（驼峰）
                                     productData.manufacturer ||       // 优先级7：制造商
                                     productData.brand ||              // 优先级8：品牌
                                     '见商品图';                        // 默认值

                    console.log(`📋 企业名称来源: ${companyName}`);
                    console.log(`📋 产品数据字段:`, {
                        supplier_name: productData.supplier_name,
                        supplierName: productData.supplierName,
                        company_name: productData.company_name,
                        companyName: productData.companyName,
                        enterprise_name: productData.enterprise_name,
                        enterpriseName: productData.enterpriseName,
                        manufacturer: productData.manufacturer,
                        brand: productData.brand
                    });

                    const companyResult = await this.fillCompanyName(companyName);
                    if (companyResult) {
                        console.log('✅ 企业名称填写成功');
                    } else {
                        console.warn('⚠️ 企业名称填写失败，但继续处理');
                    }

                    await this.sleep(1000);

                    // 4. 循环2次执行抽数和功效流程
                    console.log('🔄 开始循环2次执行抽数和功效流程...');
                    const maxCycles = 2;
                    let skuSuccess = false;
                    let effectSuccess = false;

                    for (let cycle = 1; cycle <= maxCycles; cycle++) {
                        console.log(`🔄 第${cycle}次执行抽数和功效流程...`);

                        // 4.1 新建SKU（抽数）
                        if (!skuSuccess) {
                            console.log(`🆕 第${cycle}次自动新建SKU...`);
                            const skuResult = await this.createNewSKU();
                            if (skuResult) {
                                console.log(`✅ 第${cycle}次SKU创建成功`);
                                skuSuccess = true;
                            } else {
                                console.warn(`⚠️ 第${cycle}次SKU创建失败，但继续处理`);
                            }
                            await this.sleep(1000);
                        }

                        // 4.2 选择功效（如果是纸巾类产品）
                        if (!effectSuccess && productData.name && (productData.name.includes('纸') || productData.name.includes('抽纸'))) {
                            console.log(`🧽 第${cycle}次检测到纸巾类产品，自动选择清洁去污功效...`);
                            const effectResult = await this.selectCleaningEffect();
                            if (effectResult) {
                                console.log(`✅ 第${cycle}次功效选择成功`);
                                effectSuccess = true;
                            } else {
                                console.warn(`⚠️ 第${cycle}次功效选择失败，但继续处理`);
                            }
                            await this.sleep(1000);
                        }

                        // 如果两个都成功了，就不需要再循环
                        if (skuSuccess && effectSuccess) {
                            console.log(`✅ 第${cycle}次流程中抽数和功效都已成功，跳过后续循环`);
                            break;
                        }

                        // 如果不是最后一次循环，等待一段时间再进行下次循环
                        if (cycle < maxCycles) {
                            console.log(`⏳ 等待第${cycle + 1}次流程执行...`);
                            await this.sleep(2000);
                        }

                        console.log(`✅ 第${cycle}次抽数和功效流程执行完成`);
                    }

                    console.log(`✅ 抽数和功效流程执行完成 (共${maxCycles}次尝试)`);
                    console.log(`📊 最终结果: SKU创建${skuSuccess ? '成功' : '失败'}, 功效选择${effectSuccess ? '成功' : '失败'}`);

                    await this.sleep(1000);

                    // 6. 填写包装规格
                    console.log('📦 自动填写包装规格...');
                    const specResult = await this.fillPackagingSpec();
                    if (specResult) {
                        console.log('✅ 包装规格填写成功');
                    } else {
                        console.warn('⚠️ 包装规格填写失败，但继续处理');
                    }

                    await this.sleep(1000);

                    // 7. 点击"从主图填入"
                    console.log('🖼️ 自动点击"从主图填入"...');
                    const fillResult = await this.clickFillFromMainImage();
                    if (fillResult) {
                        console.log('✅ "从主图填入"点击成功');
                    } else {
                        console.warn('⚠️ "从主图填入"点击失败，但继续处理');
                    }

                    // 完成所有自动填充
                    console.log('✅ 品牌和产品信息页面自动填充完成');
                    console.log('💡 提示：请检查填充结果，确认无误后手动点击"下一步"按钮继续');

                    // 清除品牌页面监听器，避免重复处理
                    if (this.brandPageWatcher) {
                        clearInterval(this.brandPageWatcher);
                        this.brandPageWatcher = null;
                        console.log('🧹 清除品牌页面监听器');
                    }

                    return true;

                } catch (error) {
                    console.error('❌ 自动处理品牌和产品名称页面失败:', error);
                    return false;
                }
            },

            // 监听品牌和产品名称页面
            watchForBrandAndProductPage(productData) {
                console.log('👀 开始监听品牌和产品名称页面...');

                // 清除之前的监听器（避免重复监听）
                if (this.brandPageWatcher) {
                    clearInterval(this.brandPageWatcher);
                    console.log('🧹 清除之前的品牌页面监听器');
                }

                // 创建一个定时检查器
                let checkCount = 0;
                this.brandPageWatcher = setInterval(async () => {
                    try {
                        checkCount++;
                        console.log(`🔄 品牌页面监听器检查 #${checkCount}`);

                        // 检查是否还在创建当前商品
                        if (!this.isCreatingProduct) {
                            console.log('🛑 商品创建已完成，停止品牌页面监听');
                            clearInterval(this.brandPageWatcher);
                            this.brandPageWatcher = null;
                            return;
                        }

                        // 检查是否在品牌和产品名称页面
                        const isOnBrandPage = await this.isOnBrandAndProductPage();
                        if (isOnBrandPage) {
                            console.log('🎯 检测到品牌和产品名称页面，开始自动处理...');

                            // 清除定时器
                            clearInterval(this.brandPageWatcher);
                            this.brandPageWatcher = null;

                            // 处理品牌和产品名称页面
                            await this.handleBrandAndProductPage(productData);
                        } else {
                            console.log(`⏳ 第${checkCount}次检查：尚未进入品牌和产品名称页面`);
                        }
                    } catch (error) {
                        console.error('❌ 监听品牌和产品名称页面失败:', error);
                    }
                }, 2000); // 每2秒检查一次

                // 设置最大监听时间（5分钟）
                setTimeout(() => {
                    if (this.brandPageWatcher) {
                        clearInterval(this.brandPageWatcher);
                        this.brandPageWatcher = null;
                        console.log('⏰ 品牌和产品名称页面监听超时，停止监听');
                    }
                }, 300000); // 5分钟
            },

            // 直接点击下一步按钮（用于品牌和产品名称页面）
            async clickNextStepButtonDirect() {
                try {
                    console.log('🔍 寻找下一步按钮...');

                    // 查找下一步按钮的选择器
                    const nextButtonSelectors = [
                        'button:contains("下一步")',
                        'button[class*="primary"]:contains("下一步")',
                        '.ecom-btn-primary:contains("下一步")',
                        'button[type="submit"]',
                        'button.primary'
                    ];

                    // 按钮文本关键词
                    const buttonTexts = ['下一步', '继续', '下一页', '提交', '确定'];

                    // 查找所有按钮
                    const allButtons = document.querySelectorAll('button');
                    for (const button of allButtons) {
                        // 检查按钮是否可见且可点击
                        if (button.offsetWidth === 0 || button.offsetHeight === 0 ||
                            button.disabled || button.style.display === 'none') {
                            continue;
                        }

                        const buttonText = button.textContent.trim();
                        const isNextButton = buttonTexts.some(text => buttonText.includes(text));

                        if (isNextButton) {
                            console.log(`✅ 找到下一步按钮: "${buttonText}"`);

                            // 滚动到按钮位置
                            button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            await this.sleep(500);

                            // 点击按钮
                            button.click();

                            // 等待页面响应
                            await this.sleep(1000);
                            return true;
                        }
                    }

                    console.warn('⚠️ 未找到下一步按钮');
                    return false;

                } catch (error) {
                    console.error('❌ 点击下一步按钮失败:', error);
                    return false;
                }
            },

            // 检查页面状态和元素（暂时禁用）
            async debugPageState() {
                console.log('🔍 debugPageState 方法已暂时禁用，避免意外触发页面内容');
                return false;
            },

            // 点击下一步按钮
            async clickNextStepButton() {
                try {
                    console.log('🔍 寻找下一步按钮...');

                    // 抖音小店特定的按钮选择器
                    const nextButtonSelectors = [
                        // 通用按钮选择器
                        'button[type="submit"]',
                        'button.primary',
                        'button[class*="primary"]',
                        'button[class*="btn-primary"]',

                        // 抖音小店特定选择器
                        '.ecom-btn-primary',
                        '.ecom-button-primary',
                        'button[class*="ecom"]',

                        // 通用下一步按钮
                        '.next-button',
                        '.next-btn',
                        '.submit-button',
                        '.submit-btn',

                        // 所有按钮（用于文本匹配）
                        'button'
                    ];

                    // 按钮文本关键词
                    const buttonTexts = ['下一步', '继续', '下一页', '提交', '确定', '保存并继续', '下一步：选择类目'];

                    for (const selector of nextButtonSelectors) {
                        try {
                            const buttons = document.querySelectorAll(selector);
                            for (const button of buttons) {
                                // 检查按钮是否可见且可点击
                                if (button.offsetWidth === 0 || button.offsetHeight === 0 ||
                                    button.disabled || button.style.display === 'none') {
                                    continue;
                                }

                                const buttonText = button.textContent.trim();

                                // 检查按钮文本是否匹配
                                const isNextButton = buttonTexts.some(text => buttonText.includes(text));

                                if (isNextButton) {
                                    console.log(`✅ 找到下一步按钮: "${buttonText}" (${selector})`);

                                    // 滚动到按钮位置
                                    button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    await new Promise(resolve => setTimeout(resolve, 500));

                                    // 点击按钮
                                    button.click();

                                    // 等待页面响应
                                    await new Promise(resolve => setTimeout(resolve, 300));

                                    return true;
                                }
                            }
                        } catch (e) {
                            console.warn(`选择器 ${selector} 查找失败:`, e);
                        }
                    }

                    // 如果没找到，尝试查找任何可能的提交按钮
                    console.log('🔄 尝试查找任何可能的提交按钮...');
                    const allButtons = document.querySelectorAll('button, input[type="submit"], a[role="button"]');

                    for (const button of allButtons) {
                        if (button.offsetWidth === 0 || button.offsetHeight === 0 ||
                            button.disabled || button.style.display === 'none') {
                            continue;
                        }

                        const buttonText = button.textContent.trim();
                        const buttonClass = button.className;

                        // 检查是否是主要按钮（通常是蓝色或突出显示的）
                        const isPrimaryButton = buttonClass.includes('primary') ||
                                              buttonClass.includes('btn-primary') ||
                                              buttonClass.includes('ecom-btn-primary') ||
                                              window.getComputedStyle(button).backgroundColor.includes('rgb(24, 144, 255)') ||
                                              window.getComputedStyle(button).backgroundColor.includes('rgb(22, 119, 255)');

                        if (isPrimaryButton && buttonText.length > 0) {
                            console.log(`🎯 找到主要按钮: "${buttonText}"`);
                            button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            await new Promise(resolve => setTimeout(resolve, 500));
                            button.click();
                            await new Promise(resolve => setTimeout(resolve, 300));
                            return true;
                        }
                    }

                    console.warn('⚠️ 未找到下一步按钮');
                    return false;

                } catch (error) {
                    console.error('❌ 点击下一步按钮失败:', error);
                    return false;
                }
            },

            // 智能推荐类目选择（基于读取的推荐信息）
            async searchAndSelectCategory(categoryInfo) {
                try {
                    console.log('🤖 开始智能推荐类目选择...');

                    const labelDesc = categoryInfo.labelDesc;
                    console.log(`目标类目: ${labelDesc}`);

                    // 步骤1: 读取页面上的智能推荐类目信息
                    const recommendedCategory = await this.findRecommendedCategory(labelDesc);
                    if (recommendedCategory && recommendedCategory.path) {
                        console.log(`✅ 读取到智能推荐类目: ${recommendedCategory.text}`);
                        console.log(`📋 推荐类目路径: ${recommendedCategory.path.join(' → ')}`);

                        // 步骤2: 根据推荐的路径逐级选择类目
                        const selectResult = await this.selectCategoryByPath(recommendedCategory.path);
                        if (selectResult) {
                            console.log('✅ 智能推荐类目选择成功');
                            return true;
                        }
                    }

                    // 如果没有找到推荐类目，使用缓存的类目信息
                    console.log('⚠️ 未找到智能推荐信息，使用缓存的类目数据...');

                    // 解析缓存的类目路径
                    const categoryPath = this.parseCategoryPath(labelDesc);
                    if (categoryPath.length > 0) {
                        console.log(`📋 缓存类目路径: ${categoryPath.join(' → ')}`);
                        const selectResult = await this.selectCategoryByPath(categoryPath);
                        if (selectResult) {
                            console.log('✅ 缓存类目选择成功');
                            return true;
                        }
                    }

                    // 最后的备用方案（暂时禁用，避免意外触发）
                    console.log('⚠️ 默认类目选择已暂时禁用，避免意外触发页面内容');
                    console.log('💡 提示：请手动选择类目或检查页面状态');

                } catch (error) {
                    console.error('❌ 搜索并选择类目失败:', error);
                }
            },

            // 读取页面上的智能推荐类目信息（不点击）
            async findRecommendedCategory(targetCategoryPath) {
                try {
                    console.log('🔍 读取页面智能推荐类目信息...');

                    // 等待页面加载
                    await new Promise(resolve => setTimeout(resolve, 300));

                    // 首先尝试读取智能推荐区域的信息
                    const recommendSelectors = [
                        '.style_recomTag__UoGMa',  // 弹窗中的智能推荐标签
                        '.styles_normal__ILJVk',   // 主页面推荐类目
                        '[class*="recomTag"]',     // 通用推荐标签
                        '[class*="recommend"]'     // 通用推荐区域
                    ];

                    // 尝试从推荐区域获取类目信息
                    for (const selector of recommendSelectors) {
                        const recommendItems = document.querySelectorAll(selector);
                        if (recommendItems.length > 0) {
                            console.log(`✅ 找到 ${recommendItems.length} 个推荐类目 (${selector})`);

                            for (const item of recommendItems) {
                                const text = item.textContent.trim();
                                if (text && text.length > 0 && !text.includes('推荐您采纳')) {
                                    console.log(`📋 推荐类目原文: ${text}`);

                                    // 特殊处理：如果包含完整路径（带 > 符号）
                                    if (text.includes(' > ')) {
                                        const categoryPath = this.parseCategoryPath(text);
                                        if (categoryPath.length > 0) {
                                            console.log(`✅ 解析完整推荐路径: ${categoryPath.join(' → ')}`);
                                            return {
                                                text: text,
                                                path: categoryPath,
                                                element: null
                                            };
                                        }
                                    }
                                    // 如果是简化显示（如：洗护清洁剂/卫生巾/纸/香薰 > ... > 普通抽纸）
                                    else if (text.includes('...')) {
                                        // 提取首尾信息，构建完整路径
                                        const parts = text.split(' > ');
                                        if (parts.length >= 3) {
                                            const firstLevel = parts[0].trim();
                                            const lastLevel = parts[parts.length - 1].trim();

                                            // 根据首尾信息推断完整路径
                                            if (firstLevel.includes('洗护清洁剂') && lastLevel === '普通抽纸') {
                                                console.log(`🎯 检测到智能推荐: ${firstLevel} -> ${lastLevel}`);
                                                const fullPath = [
                                                    '洗护清洁剂/卫生巾/纸/香薰',
                                                    '纸品/湿巾',
                                                    '抽纸',
                                                    '壁挂抽纸'  // 智能替换：普通抽纸 -> 壁挂抽纸
                                                ];
                                                console.log(`🔄 ✅ 智能推荐路径替换: 普通抽纸 -> 壁挂抽纸`);
                                                console.log(`📋 推断完整路径: ${fullPath.join(' → ')}`);
                                                return {
                                                    text: text,
                                                    path: fullPath,
                                                    element: null
                                                };
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 如果没有找到推荐信息，返回null让后续使用手动选择
                    console.log('⚠️ 未找到智能推荐信息');
                    return null;

                    for (const selector of categorySelectors) {
                        const items = document.querySelectorAll(selector);
                        if (items.length > 0) {
                            console.log(`✅ 找到 ${items.length} 个类目选项 (${selector})`);

                            // 分析每个类目选项，选择第一个匹配的
                            for (const item of items) {
                                const text = item.textContent.trim();
                                if (text && text.length > 0) {
                                    console.log(`🔍 分析类目: ${text}`);

                                    // 计算与目标类目的匹配度
                                    const score = this.calculateCategoryScore(text, '', targetCategoryPath);
                                    if (score > 30) { // 降低阈值，更容易匹配
                                        console.log(`✅ 找到匹配的类目: ${text} (得分: ${score})`);
                                        return {
                                            element: item,
                                            text: text,
                                            score: score
                                        };
                                    }
                                }
                            }
                        }
                    }

                    // 如果没有找到匹配的类目，选择第一个可见的类目
                    console.log('🔍 未找到匹配类目，选择第一个可见类目...');
                    for (const selector of categorySelectors) {
                        const items = document.querySelectorAll(selector);
                        if (items.length > 0) {
                            const firstItem = items[0];
                            const text = firstItem.textContent.trim();
                            if (text && text.length > 0) {
                                console.log(`✅ 选择第一个可见类目: ${text} (${selector})`);
                                return {
                                    element: firstItem,
                                    text: text,
                                    score: 10
                                };
                            }
                        }
                    }

                    console.log('❌ 未找到任何可用的类目');
                    return null;

                } catch (error) {
                    console.error('❌ 查找推荐类目失败:', error);
                    return null;
                }
            },

            // 解析类目路径
            parseCategoryPath(categoryText) {
                try {
                    console.log(`🔍 解析类目文本: ${categoryText}`);

                    // 处理不同的分隔符格式
                    let path = [];

                    if (categoryText.includes(' > ')) {
                        // 格式: "洗护清洁剂/卫生巾/纸/香薰 > 纸品/湿巾 > 抽纸 > 普通抽纸"
                        path = categoryText.split(' > ').map(item => item.trim());
                    } else if (categoryText.includes('/')) {
                        // 格式: "洗护清洁剂/卫生巾/纸/香薰" - 这是一级类目，需要补充其他级别
                        const firstLevel = categoryText.trim();

                        // 根据一级类目推断完整路径
                        if (firstLevel.includes('洗护清洁剂') || firstLevel.includes('纸') || firstLevel.includes('香薰')) {
                            // 纸品相关的完整路径（智能替换：普通抽纸 -> 壁挂抽纸）
                            path = [
                                '洗护清洁剂/卫生巾/纸/香薰',
                                '纸品/湿巾',
                                '抽纸',
                                '壁挂抽纸'  // 智能替换：普通抽纸 -> 壁挂抽纸
                            ];
                            console.log(`🔄 默认路径智能替换: 普通抽纸 -> 壁挂抽纸`);
                        } else {
                            // 其他情况，只使用一级类目
                            path = [firstLevel];
                        }
                    } else {
                        // 单级类目
                        path = [categoryText.trim()];
                    }

                    // 过滤空项和省略号
                    path = path.filter(item => item && item !== '...' && item.length > 0);

                    // 智能替换：如果第四级是普通抽纸，改为壁挂抽纸
                    console.log(`🔍 检查第四级类目替换: path.length=${path.length}, 第四级=${path[3] || '无'}`);
                    if (path.length >= 4 && path[3] === '普通抽纸') {
                        const originalPath = [...path];
                        path[3] = '壁挂抽纸';
                        console.log(`🔄 ✅ 解析时智能替换第四级类目:`);
                        console.log(`  原路径: ${originalPath.join(' → ')}`);
                        console.log(`  新路径: ${path.join(' → ')}`);
                    } else if (path.length >= 4) {
                        console.log(`ℹ️ 第四级类目无需替换: ${path[3]}`);
                    }

                    console.log(`📋 解析结果: ${path.join(' → ')}`);
                    return path;

                } catch (error) {
                    console.error('❌ 解析类目路径失败:', error);
                    return [];
                }
            },

            // 根据路径逐级选择类目（带重试机制）
            async selectCategoryByPath(categoryPath, maxRetries = 2) {
                for (let attempt = 0; attempt < maxRetries; attempt++) {
                    try {
                        console.log(`🎯 开始逐级选择类目... (尝试 ${attempt + 1}/${maxRetries})`);

                        if (!categoryPath || categoryPath.length === 0) {
                            console.warn('⚠️ 类目路径为空');
                            return false;
                        }

                        let allLevelsSuccess = true;

                        // 逐级选择类目
                        for (let level = 0; level < categoryPath.length; level++) {
                            let categoryName = categoryPath[level];
                            console.log(`🔍 选择第${level + 1}级类目: ${categoryName}`);

                            // 如果是四级类目，先检查是否可用，并进行智能替换
                            if (level + 1 === 4) {
                                console.log(`📋 第四级类目处理: ${categoryName}`);
                                const fourthLevelAvailable = await this.checkFourthLevelAvailable();
                                if (!fourthLevelAvailable) {
                                    console.log(`⚠️ 四级类目暂不可用，跳过: ${categoryName}`);
                                    break;
                                }

                                // 智能替换：普通抽纸 -> 壁挂抽纸
                                if (categoryName === '普通抽纸') {
                                    const originalName = categoryName;
                                    categoryName = '壁挂抽纸';
                                    console.log(`🔄 ✅ 智能替换第四级类目: ${originalName} -> ${categoryName}`);
                                    console.log(`🎯 替换后将查找: ${categoryName}`);
                                } else {
                                    console.log(`ℹ️ 第四级类目无需替换: ${categoryName}`);
                                }
                            }

                            // 查找并点击当前级别的类目（带重试）
                            const success = await this.selectCategoryAtLevel(categoryName, level + 1, 3);
                            if (!success) {
                                console.warn(`⚠️ 第${level + 1}级类目选择失败: ${categoryName}`);
                                allLevelsSuccess = false;
                                break;
                            }

                            // 等待下一级类目加载
                            if (level < categoryPath.length - 1) {
                                console.log('⏳ 等待下一级类目加载...');

                                // 如果下一级是四级类目，需要特殊处理
                                if (level + 2 === 4) {
                                    console.log('🔍 准备加载四级类目，等待更长时间...');
                                    await new Promise(resolve => setTimeout(resolve, 800));

                                    // 检查四级类目是否已经出现
                                    const fourthLevelReady = await this.waitForFourthLevelCategory();
                                    if (!fourthLevelReady) {
                                        console.warn(`⚠️ 四级类目加载超时，但继续尝试...`);
                                    }
                                } else {
                                    await new Promise(resolve => setTimeout(resolve, 300));

                                    // 额外等待确保类目完全加载
                                    const loadSuccess = await this.waitForCategoryLoad(level + 2);
                                    if (!loadSuccess) {
                                        console.warn(`⚠️ 第${level + 2}级类目加载超时，继续尝试...`);
                                    }
                                }
                            }
                        }

                        // 如果所有级别都选择成功，继续确认
                        if (allLevelsSuccess) {

                            // 最后点击确定按钮
                            console.log('🔘 点击确定按钮...');
                            const confirmResult = await this.confirmCategoryModal();
                            if (confirmResult) {
                                console.log('✅ 类目确定成功');

                                // 验证类目选择结果
                                const verifySuccess = await this.verifyCategorySelection(categoryPath);
                                if (verifySuccess) {
                                    console.log('✅ 类目选择和验证都成功完成');
                                } else {
                                    console.log('⚠️ 类目选择可能未完全成功，但继续执行');
                                }

                                // 点击下一步按钮
                                console.log('⏳ 等待页面更新...');
                                await new Promise(resolve => setTimeout(resolve, 500));

                                // 确认类目选择（关闭弹窗）
                                console.log('🔘 确认类目选择，关闭弹窗...');
                                const confirmSuccess = await this.confirmCategoryModal();
                                if (confirmSuccess) {
                                    console.log('✅ 类目选择弹窗已关闭');
                                } else {
                                    console.log('ℹ️ 未找到弹窗确定按钮，可能弹窗已自动关闭');
                                }

                                // 等待弹窗关闭完成
                                await new Promise(resolve => setTimeout(resolve, 1000));

                                // 等待"下一步"按钮变为可用状态，然后点击
                                console.log('⏳ 类目选择完成，等待"下一步"按钮变为可用状态...');
                                const nextStepResult = await this.waitAndClickNextStepButton();
                                if (nextStepResult) {
                                    console.log('✅ 已点击下一步，类目选择流程完成');
                                    return true;
                                } else {
                                    console.warn('⚠️ 点击下一步按钮失败，请手动点击');
                                    console.log('💡 提示：请手动点击页面上的"下一步"按钮继续');
                                    return false;
                                }
                            }
                        }

                        // 如果这次尝试失败，等待后重试
                        if (attempt < maxRetries - 1) {
                            console.log(`⏳ 第${attempt + 1}次尝试失败，等待后重试...`);
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }

                    } catch (error) {
                        console.error(`❌ 逐级选择类目失败 (尝试 ${attempt + 1}):`, error);
                        if (attempt < maxRetries - 1) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    }
                }

                console.warn(`⚠️ 经过${maxRetries}次尝试，类目选择仍然失败`);
                return false;
            },

            // 选择指定级别的类目（手动点击方式）
            async selectCategoryAtLevel(categoryName, level, maxRetries = 3) {
                for (let retry = 0; retry < maxRetries; retry++) {
                    try {
                        console.log(`🔍 在第${level}级中查找: ${categoryName} (尝试 ${retry + 1}/${maxRetries})`);

                        // 根据级别选择不同的选择器
                        let selectors = [];
                        switch (level) {
                            case 1:
                                selectors = [
                                    '.style_cat1__GxbQb .style_catItem__gjkVg',
                                    '.style_catItem__gjkVg'
                                ];
                                break;
                            case 2:
                                selectors = [
                                    '.style_cat2__j3Rto .style_catItem__gjkVg',
                                    '.style_cat2__j3Rto [class*="catItem"]'
                                ];
                                break;
                            case 3:
                                selectors = [
                                    '.style_cat3__afwud .style_catItem__gjkVg',
                                    '.style_cat3__afwud [class*="catItem"]'
                                ];
                                break;
                            case 4:
                                // 四级类目的选择器（基于HTML分析）
                                selectors = [
                                    '.overflow-scoll_overflowScroll__igXmX:last-child .style_catItem__gjkVg',
                                    '.style_cat3__afwud:last-child .style_catItem__gjkVg',
                                    '.style_cat3__afwud .style_catItem__gjkVg',
                                    '[class*="cat"]:last-child .style_catItem__gjkVg'
                                ];
                                break;
                            default:
                                selectors = ['[class*="catItem"]'];
                        }

                        // 查找匹配的类目
                        let found = false;
                        for (const selector of selectors) {
                            const items = document.querySelectorAll(selector);
                            if (items.length > 0) {
                                console.log(`🔍 在选择器 ${selector} 中找到 ${items.length} 个类目`);

                                // 调试：输出所有可用的类目选项
                                const availableCategories = Array.from(items).map(item => item.textContent.trim()).filter(text => text);
                                console.log(`📋 第${level}级可用类目:`, availableCategories);

                                for (const item of items) {
                                    const text = item.textContent.trim();
                                    if (text && this.isCategoryMatch(text, categoryName)) {
                                        console.log(`✅ 找到匹配类目: ${text} (目标: ${categoryName})`);
                                        item.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        await new Promise(resolve => setTimeout(resolve, 100));
                                        item.click();
                                        await new Promise(resolve => setTimeout(resolve, 150));
                                        found = true;
                                        break;
                                    }
                                }

                                if (found) break;
                            }
                        }

                        if (found) return true;

                        // 如果没找到精确匹配，尝试选择最相似的类目
                        if (retry === maxRetries - 1) {
                            console.log(`🔍 尝试模糊匹配第${level}级类目: ${categoryName}`);
                            for (const selector of selectors) {
                                const items = document.querySelectorAll(selector);
                                if (items.length > 0) {
                                    let bestMatch = null;
                                    let bestScore = 0;

                                    for (const item of items) {
                                        const text = item.textContent.trim();
                                        if (text) {
                                            const score = this.calculateSimilarity(text, categoryName);
                                            if (score > bestScore && score > 0.3) {
                                                bestScore = score;
                                                bestMatch = item;
                                            }
                                        }
                                    }

                                    if (bestMatch) {
                                        const matchText = bestMatch.textContent.trim();
                                        console.log(`✅ 找到最佳匹配类目: ${matchText} (相似度: ${bestScore.toFixed(2)})`);
                                        bestMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                        await new Promise(resolve => setTimeout(resolve, 100));
                                        bestMatch.click();
                                        await new Promise(resolve => setTimeout(resolve, 150));
                                        return true;
                                    }
                                }
                            }
                        }

                        // 如果没找到，等待一下再重试
                        if (retry < maxRetries - 1) {
                            console.log(`⏳ 第${retry + 1}次尝试未找到，等待后重试...`);
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }

                    } catch (error) {
                        console.error(`❌ 选择第${level}级类目失败 (尝试 ${retry + 1}):`, error);
                        if (retry < maxRetries - 1) {
                            await new Promise(resolve => setTimeout(resolve, 300));
                        }
                    }
                }

                console.warn(`⚠️ 经过${maxRetries}次尝试，未找到第${level}级类目: ${categoryName}`);
                return false;
            },



            // 等待四级类目加载
            async waitForFourthLevelCategory(maxWaitTime = 3000) {
                try {
                    console.log('⏳ 等待四级类目加载...');

                    const startTime = Date.now();
                    while (Date.now() - startTime < maxWaitTime) {
                        // 检查四级类目的各种可能选择器
                        const fourthLevelSelectors = [
                            '.overflow-scoll_overflowScroll__igXmX:last-child .style_catItem__gjkVg',
                            '.style_cat3__afwud:last-child .style_catItem__gjkVg',
                            'input[placeholder*="搜索四级类目"]',
                            '.overflow-scoll_overflowScroll__igXmX:nth-child(4) .style_catItem__gjkVg'
                        ];

                        for (const selector of fourthLevelSelectors) {
                            const items = document.querySelectorAll(selector);
                            if (items.length > 0) {
                                // 检查是否有实际的类目内容
                                const hasContent = Array.from(items).some(item => {
                                    const text = item.textContent.trim();
                                    return text && text.length > 0;
                                });

                                if (hasContent) {
                                    console.log(`✅ 四级类目已加载，找到 ${items.length} 个选项 (${selector})`);
                                    const categories = Array.from(items).map(item => item.textContent.trim()).filter(text => text);
                                    console.log(`📋 四级类目选项:`, categories);
                                    return true;
                                }
                            }
                        }

                        // 每100毫秒检查一次
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    console.warn('⚠️ 四级类目加载超时');
                    return false;

                } catch (error) {
                    console.error('❌ 等待四级类目加载失败:', error);
                    return false;
                }
            },

            // 检测四级类目是否可用
            async checkFourthLevelAvailable() {
                try {
                    // 查找四级类目的指示器
                    const fourthLevelIndicators = [
                        'input[placeholder*="搜索四级类目"]',
                        '.overflow-scoll_overflowScroll__igXmX:nth-child(4)',
                        '.style_cat3__afwud:last-child',
                        // 检查是否有四个类目容器
                        '.style_selectTree__McRTS > div:nth-child(4)'
                    ];

                    for (const selector of fourthLevelIndicators) {
                        const element = document.querySelector(selector);
                        if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
                            console.log(`✅ 检测到四级类目可用: ${selector}`);
                            return true;
                        }
                    }

                    // 检查类目容器数量
                    const categoryContainers = document.querySelectorAll('.overflow-scoll_overflowScroll__igXmX');
                    console.log(`📊 检测到 ${categoryContainers.length} 个类目容器`);

                    if (categoryContainers.length >= 4) {
                        console.log(`✅ 根据容器数量判断四级类目可用`);
                        return true;
                    }

                    console.log(`⚠️ 四级类目暂不可用`);
                    return false;

                } catch (error) {
                    console.error(`❌ 检测四级类目失败:`, error);
                    return false;
                }
            },

            // 智能类目匹配
            isCategoryMatch(availableText, targetText) {
                if (!availableText || !targetText) return false;

                console.log(`🔍 类目匹配检查: "${availableText}" vs "${targetText}"`);

                // 特殊处理：如果目标是"壁挂抽纸"，不能匹配到"普通抽纸"
                if (targetText === '壁挂抽纸' && availableText.includes('普通')) {
                    console.log(`❌ 拒绝匹配: 目标是"壁挂抽纸"，但找到的是"${availableText}"`);
                    return false;
                }

                // 特殊处理：如果目标是"普通抽纸"，不能匹配到"壁挂抽纸"
                if (targetText === '普通抽纸' && availableText.includes('壁挂')) {
                    console.log(`❌ 拒绝匹配: 目标是"普通抽纸"，但找到的是"${availableText}"`);
                    return false;
                }

                // 完全匹配
                if (availableText === targetText) {
                    console.log(`✅ 完全匹配: ${availableText}`);
                    return true;
                }

                // 包含匹配
                if (availableText.includes(targetText) || targetText.includes(availableText)) {
                    console.log(`✅ 包含匹配: ${availableText} <-> ${targetText}`);
                    return true;
                }

                // 特殊匹配规则
                const specialMatches = {
                    '洗护清洁剂/卫生巾/纸/香薰': ['洗护清洁剂', '纸', '香薰', '卫生巾'],
                    '纸品/湿巾': ['纸品', '湿巾', '纸'],
                    '抽纸': ['抽纸', '面巾纸'],
                    '普通抽纸': ['普通抽纸', '抽纸', '面巾纸', '普通'],
                    '壁挂抽纸': ['壁挂抽纸', '壁挂', '抽纸', '面巾纸'],  // 增强壁挂抽纸的匹配
                    // 反向匹配
                    '普通': ['普通抽纸'],
                    '壁挂': ['壁挂抽纸']
                };

                // 检查特殊匹配
                for (const [key, values] of Object.entries(specialMatches)) {
                    if (targetText === key) {
                        return values.some(val => availableText.includes(val));
                    }
                    if (availableText === key) {
                        return values.some(val => targetText.includes(val));
                    }
                }

                // 关键词匹配
                const targetKeywords = targetText.split(/[\/\s\-]+/).filter(k => k.length > 1);
                const availableKeywords = availableText.split(/[\/\s\-]+/).filter(k => k.length > 1);

                // 如果有超过一半的关键词匹配，认为匹配
                const matchCount = targetKeywords.filter(tk =>
                    availableKeywords.some(ak => ak.includes(tk) || tk.includes(ak))
                ).length;

                return matchCount > 0 && matchCount >= Math.min(targetKeywords.length * 0.5, 1);
            },

            // 计算文本相似度
            calculateSimilarity(text1, text2) {
                if (!text1 || !text2) return 0;

                // 转换为小写进行比较
                const t1 = text1.toLowerCase();
                const t2 = text2.toLowerCase();

                // 完全匹配
                if (t1 === t2) return 1.0;

                // 包含匹配
                if (t1.includes(t2) || t2.includes(t1)) return 0.8;

                // 关键词匹配
                const words1 = t1.split(/[\/\s\-\u4e00-\u9fff]+/).filter(w => w.length > 0);
                const words2 = t2.split(/[\/\s\-\u4e00-\u9fff]+/).filter(w => w.length > 0);

                if (words1.length === 0 || words2.length === 0) return 0;

                let matchCount = 0;
                for (const w1 of words1) {
                    for (const w2 of words2) {
                        if (w1.includes(w2) || w2.includes(w1)) {
                            matchCount++;
                            break;
                        }
                    }
                }

                return matchCount / Math.max(words1.length, words2.length);
            },

            // 等待指定级别的类目加载完成
            async waitForCategoryLoad(level, maxWaitTime = 2000) {
                try {
                    console.log(`⏳ 等待第${level}级类目加载完成...`);

                    // 根据级别确定选择器
                    let selector = '';
                    switch (level) {
                        case 2:
                            selector = '.style_cat2__j3Rto .style_catItem__gjkVg';
                            break;
                        case 3:
                            selector = '.style_cat3__afwud .style_catItem__gjkVg';
                            break;
                        case 4:
                            selector = '.overflow-scoll_overflowScroll__igXmX:last-child .style_catItem__gjkVg, .style_cat3__afwud:last-child .style_catItem__gjkVg, .style_cat3__afwud .style_catItem__gjkVg';
                            break;
                        default:
                            selector = '[class*="catItem"]';
                    }

                    const startTime = Date.now();
                    while (Date.now() - startTime < maxWaitTime) {
                        const items = document.querySelectorAll(selector);
                        if (items.length > 0) {
                            console.log(`✅ 第${level}级类目已加载，找到 ${items.length} 个选项`);
                            return true;
                        }

                        // 每50毫秒检查一次
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }

                    console.warn(`⚠️ 第${level}级类目加载超时`);
                    return false;

                } catch (error) {
                    console.error(`❌ 等待第${level}级类目加载失败:`, error);
                    return false;
                }
            },

            // 验证类目选择是否成功
            async verifyCategorySelection(expectedPath) {
                try {
                    console.log('🔍 验证类目选择结果...');

                    // 等待页面更新
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // 查找显示当前选择类目的元素
                    const categoryDisplaySelectors = [
                        '.category-display',
                        '.selected-category',
                        '.category-path',
                        '[class*="category"][class*="selected"]',
                        '[class*="breadcrumb"]'
                    ];

                    for (const selector of categoryDisplaySelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            const displayText = element.textContent.trim();
                            console.log(`📋 当前显示的类目: ${displayText}`);

                            // 检查是否包含预期的类目路径
                            const pathMatches = expectedPath.some(category =>
                                displayText.includes(category)
                            );

                            if (pathMatches) {
                                console.log('✅ 类目选择验证成功');
                                return true;
                            }
                        }
                    }

                    console.log('⚠️ 无法验证类目选择结果');
                    return false;

                } catch (error) {
                    console.error('❌ 验证类目选择失败:', error);
                    return false;
                }
            },

            // 根据关键词搜索类目
            async searchCategoryByKeyword(keyword) {
                try {
                    console.log(`🔍 搜索类目关键词: ${keyword}`);

                    // 基于搜索结果页面HTML分析的搜索输入框选择器
                    const searchSelectors = [
                        // 抖音小店类目搜索框（基于HTML分析）
                        'input[placeholder="请输入商品名/类目关键词搜索"]',
                        '.style_searchInput__oUuIl',
                        '.style_searchBox__de_j8 input',
                        // 备用选择器
                        'input[placeholder*="搜索类目"]',
                        'input[placeholder*="请输入类目"]',
                        'input[placeholder*="搜索"]',
                        '.category-search input',
                        '.search-input',
                        'input[type="search"]'
                    ];

                    let searchInput = null;
                    for (const selector of searchSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
                            searchInput = element;
                            console.log(`✅ 找到搜索输入框: ${selector}`);
                            break;
                        }
                    }

                    if (!searchInput) {
                        console.warn('⚠️ 未找到搜索输入框');
                        console.log('🔍 尝试查找所有input元素...');
                        const allInputs = document.querySelectorAll('input');
                        console.log(`找到 ${allInputs.length} 个input元素`);
                        allInputs.forEach((input, index) => {
                            console.log(`Input ${index}: placeholder="${input.placeholder}", class="${input.className}"`);
                        });
                        return { success: false, error: '未找到搜索输入框' };
                    }

                    // 清空并输入关键词
                    console.log(`📝 在搜索框中输入关键词: "${keyword}"`);
                    console.log(`🔍 关键词长度: ${keyword.length} 字符`);

                    // 清空搜索框
                    searchInput.value = '';
                    searchInput.focus();

                    // 等待焦点设置
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // 直接设置值并触发事件
                    searchInput.value = keyword;
                    console.log(`✅ 搜索框当前值: "${searchInput.value}"`);

                    // 触发所有相关事件
                    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                    searchInput.dispatchEvent(new Event('change', { bubbles: true }));
                    searchInput.dispatchEvent(new Event('keyup', { bubbles: true }));

                    // 模拟按键事件
                    searchInput.dispatchEvent(new KeyboardEvent('keydown', {
                        key: 'Enter',
                        keyCode: 13,
                        bubbles: true
                    }));

                    // 触发搜索
                    searchInput.dispatchEvent(new Event('change', { bubbles: true }));
                    searchInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
                    searchInput.dispatchEvent(new KeyboardEvent('keypress', { key: 'Enter', bubbles: true }));

                    console.log(`⏳ 等待搜索结果加载...`);
                    // 等待搜索结果加载
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 检查搜索结果是否加载
                    const searchResultContainer = document.querySelector('.style_resultList__ve7wC');
                    if (searchResultContainer) {
                        console.log(`✅ 搜索结果容器已找到`);
                        const resultItems = searchResultContainer.querySelectorAll('.style_resultItem__C89MX');
                        console.log(`📋 找到 ${resultItems.length} 个搜索结果`);

                        return {
                            success: resultItems.length > 0,
                            results: Array.from(resultItems),
                            keyword: keyword
                        };
                    } else {
                        console.warn('⚠️ 未找到搜索结果容器');
                        return { success: false, error: '未找到搜索结果' };
                    }

                } catch (error) {
                    console.error(`❌ 搜索关键词 "${keyword}" 失败:`, error);
                    return { success: false, error: error.message };
                }
            },

            // 智能选择类目 - 精简版
            async selectFromSearchResults(keyword, targetCategoryPath = null) {
                try {
                    console.log(`🔍 智能选择类目，关键词: "${keyword}"`);
                    if (targetCategoryPath) {
                        console.log(`🎯 目标路径: ${targetCategoryPath}`);
                    }

                    // 等待搜索结果加载
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    // 查找搜索结果
                    const searchResultItems = document.querySelectorAll('.style_resultList__ve7wC .style_resultItem__C89MX');
                    console.log(`✅ 找到 ${searchResultItems.length} 个搜索结果`);

                    if (searchResultItems.length === 0) {
                        return { success: false, error: '未找到搜索结果' };
                    }

                    // 智能推荐：找到最匹配的类目
                    let bestMatch = null;
                    let bestScore = 0;
                    let validResults = [];

                    for (const item of searchResultItems) {
                        const text = item.textContent.trim();
                        console.log(`🔍 分析: ${text}`);

                        // 核心匹配逻辑：简单而有效
                        const score = this.calculateCategoryScore(text, keyword, targetCategoryPath);

                        if (score > 0) {
                            validResults.push({ element: item, text: text, score: score });

                            if (score > bestScore) {
                                bestScore = score;
                                bestMatch = { element: item, text: text, score: score };
                            }
                        }
                    }

                    console.log(`📋 有效结果: ${validResults.length}/${searchResultItems.length}`);
                    if (validResults.length > 0) {
                        console.log(`🏆 最佳匹配: ${bestMatch.text} (${bestMatch.score}分)`);
                    }

                    if (bestMatch && bestScore > 0) {
                        console.log(`✅ 选择最佳匹配: ${bestMatch.text} (得分: ${bestMatch.score})`);

                        // 点击选择该搜索结果
                        console.log('🔘 点击搜索结果...');
                        bestMatch.element.click();

                        // 等待页面响应和类目树更新（增加延迟时间）
                        console.log('⏳ 等待类目树更新...');
                        await new Promise(resolve => setTimeout(resolve, 4000));

                        return {
                            success: true,
                            selectedCategory: bestMatch.text,
                            element: bestMatch.element,
                            score: bestMatch.score
                        };
                    }

                    // 如果没有找到好的匹配，选择第一个结果
                    if (searchResultItems.length > 0) {
                        const firstItem = searchResultItems[0];
                        const firstText = firstItem.textContent.trim();
                        console.log(`⚠️ 未找到高分匹配，选择第一个结果: ${firstText}`);

                        console.log('🔘 点击第一个搜索结果...');
                        firstItem.click();

                        console.log('⏳ 等待类目树更新...');
                        await new Promise(resolve => setTimeout(resolve, 4000));

                        return {
                            success: true,
                            selectedCategory: firstText,
                            element: firstItem,
                            score: 0
                        };
                    }

                    return { success: false, error: '未找到合适的搜索结果' };

                } catch (error) {
                    console.error('❌ 从搜索结果选择失败:', error);
                    return { success: false, error: error.message };
                }
            },

            // 核心评分算法 - 简洁高效
            calculateCategoryScore(resultText, keyword, targetPath) {
                let score = 0;

                // 1. 基础匹配：包含搜索关键词
                if (resultText.includes(keyword)) {
                    score += 50;
                    console.log(`  ✅ 包含关键词"${keyword}": +50分`);
                }

                // 2. 路径匹配：与目标路径的匹配度
                if (targetPath) {
                    const targetKeywords = targetPath.split('/').map(k => k.trim()).filter(k => k);
                    let matchCount = 0;

                    for (const targetKeyword of targetKeywords) {
                        if (resultText.includes(targetKeyword)) {
                            matchCount++;
                        }
                    }

                    const matchRatio = matchCount / targetKeywords.length;
                    const pathScore = Math.floor(matchRatio * 100);
                    score += pathScore;

                    console.log(`  📊 路径匹配: ${matchCount}/${targetKeywords.length} = ${(matchRatio*100).toFixed(1)}% (+${pathScore}分)`);

                    // 3. 排除明显不匹配的类目
                    if (this.isInvalidCategory(resultText, targetKeywords)) {
                        score = 0;
                        console.log(`  ❌ 无效类目，得分清零`);
                    }
                }

                console.log(`  📊 总得分: ${score}`);
                return score;
            },

            // 简单的无效类目检查
            isInvalidCategory(resultText, targetKeywords) {
                // 推断目标领域
                const targetDomain = this.getSimpleDomain(targetKeywords);

                // 检查是否包含其他领域的明显标识
                const otherDomains = {
                    '汽车': ['汽车用品', '车载', '汽车', '车用'],
                    '电子': ['电子产品', '数码产品', '手机配件'],
                    '服装': ['服装鞋帽', '时尚服饰'],
                    '食品': ['食品饮料', '休闲食品'],
                    '办公': ['办公用品', '文具用品']
                };

                for (const [domain, keywords] of Object.entries(otherDomains)) {
                    if (domain !== targetDomain) {
                        for (const keyword of keywords) {
                            if (resultText.includes(keyword)) {
                                console.log(`    ❌ 检测到其他领域"${domain}": ${keyword}`);
                                return true;
                            }
                        }
                    }
                }

                return false;
            },

            // 简单的领域推断
            getSimpleDomain(targetKeywords) {
                const text = targetKeywords.join(' ').toLowerCase();

                if (/纸|巾|清洁|洗护|香薰|湿巾/.test(text)) return '日用品';
                if (/服装|衣|裤|裙/.test(text)) return '服装';
                if (/电子|数码|手机/.test(text)) return '电子';
                if (/食品|饮料|零食/.test(text)) return '食品';
                if (/汽车|车载/.test(text)) return '汽车';

                return '日用品';
            },

            // 获取类目搜索结果 - 基于抖音小店页面结构
            getCategorySearchResults() {
                try {
                    const results = [];

                    // 基于页面分析的抖音小店类目选择器
                    const resultSelectors = [
                        // 抖音小店类目树节点
                        '.style_catItem__gjkVg',
                        '.style_cat1__GxbQb .style_catItem__gjkVg',
                        '.style_cat2__j3Rto .style_catItem__gjkVg',
                        '.style_cat3__afwud .style_catItem__gjkVg',
                        // 通用类目选择器
                        '.category-result-item',
                        '.category-item',
                        '.search-result-item',
                        '.category-list-item',
                        '.category-option',
                        'li[data-category]',
                        '.category-tree-node',
                        '[class*="category"][class*="item"]'
                    ];

                    for (const selector of resultSelectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            console.log(`✅ 找到 ${elements.length} 个类目元素: ${selector}`);

                            elements.forEach((element, index) => {
                                // 排除禁用的类目
                                if (element.classList.contains('style_disableCategory__NoDpK') ||
                                    element.classList.contains('style_disableCategory__pRYE_')) {
                                    return;
                                }

                                const text = element.textContent.trim();
                                if (text && text.length > 0 &&
                                    element.offsetWidth > 0 && element.offsetHeight > 0) {
                                    results.push({
                                        element: element,
                                        text: text,
                                        index: index,
                                        selector: selector,
                                        isClickable: !element.disabled
                                    });
                                }
                            });

                            if (results.length > 0) {
                                break; // 找到可用结果就停止
                            }
                        }
                    }

                    console.log(`📋 获取到 ${results.length} 个可用类目结果`);
                    return results;

                } catch (error) {
                    console.error('❌ 获取搜索结果失败:', error);
                    return [];
                }
            },



            // 确认类目弹窗 - 点击类目的"确定"按钮（第二次确认）
            async confirmCategoryModal() {
                try {
                    console.log('🔍 查找类目弹窗的"确定"按钮...');

                    // 等待弹窗加载
                    await new Promise(resolve => setTimeout(resolve, 300));

                    // 类目选择弹窗的确定按钮选择器
                    const modalConfirmSelectors = [
                        // 抖音小店类目选择弹窗的确定按钮
                        '.styles_btnWrapper__akKk2 button.ecom-g-btn-primary',
                        '.ecom-g-modal-footer button.ecom-g-btn-primary',
                        '.ecom-g-modal-footer .ecom-g-btn-primary',
                        // 通用弹窗确定按钮
                        '.ecom-g-modal button.ecom-g-btn-primary',
                        'button.ecom-g-btn-primary'
                    ];

                    for (const selector of modalConfirmSelectors) {
                        const confirmButton = document.querySelector(selector);
                        if (confirmButton && !confirmButton.disabled &&
                            confirmButton.offsetWidth > 0 && confirmButton.offsetHeight > 0) {

                            const buttonText = confirmButton.textContent.trim();
                            console.log(`✅ 找到弹窗按钮: ${buttonText} (${selector})`);

                            // 检查按钮是否包含确定相关文本
                            if (buttonText.includes('确定') || buttonText.includes('确认') ||
                                buttonText.includes('选择') || buttonText.includes('完成')) {

                                console.log(`🔘 点击弹窗"${buttonText}"按钮`);
                                confirmButton.click();

                                // 等待弹窗关闭
                                await new Promise(resolve => setTimeout(resolve, 500));
                                return true;
                            }
                        }
                    }

                    // 如果没有找到特定选择器，尝试查找所有包含"确定"的按钮
                    const allButtons = document.querySelectorAll('button');
                    console.log(`🔍 在所有 ${allButtons.length} 个按钮中查找"确定"...`);

                    for (const button of allButtons) {
                        const buttonText = button.textContent.trim();
                        if ((buttonText.includes('确定') || buttonText.includes('确认')) &&
                            !button.disabled && button.offsetWidth > 0 && button.offsetHeight > 0) {

                            console.log(`✅ 找到"确定"按钮: ${buttonText}`);
                            button.click();
                            await new Promise(resolve => setTimeout(resolve, 1500));
                            return true;
                        }
                    }

                    console.log('ℹ️ 未找到类目弹窗的"确定"按钮');
                    return false;

                } catch (error) {
                    console.error('❌ 点击类目"确定"按钮失败:', error);
                    return false;
                }
            },

            // 等待并点击"下一步"按钮（类目选择完成后）
            async waitAndClickNextStepButton() {
                try {
                    console.log('⏳ 等待"下一步"按钮变为可用状态...');

                    // 等待最多10秒，检查"下一步"按钮是否可用
                    const maxWaitTime = 10000; // 10秒
                    const checkInterval = 500; // 每500ms检查一次
                    let waitedTime = 0;

                    while (waitedTime < maxWaitTime) {
                        // 查找"下一步"按钮
                        const nextButton = this.findEnabledNextStepButton();

                        if (nextButton) {
                            console.log('✅ 找到可用的"下一步"按钮，准备点击');
                            console.log(`📋 按钮信息: "${nextButton.textContent.trim()}" - ${nextButton.className}`);

                            // 滚动到按钮位置
                            nextButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            await new Promise(resolve => setTimeout(resolve, 300));

                            // 点击按钮
                            console.log('🔘 点击"下一步"按钮');
                            nextButton.click();

                            // 等待页面响应
                            await new Promise(resolve => setTimeout(resolve, 2000)); // 增加等待时间
                            console.log('✅ "下一步"按钮点击完成，等待页面跳转...');

                            // 自动检测并处理品牌和产品名称页面
                            console.log('🔍 检查是否进入品牌和产品名称页面...');
                            await new Promise(resolve => setTimeout(resolve, 1000)); // 额外等待页面加载

                            const isOnBrandPage = await this.isOnBrandAndProductPage();
                            if (isOnBrandPage) {
                                console.log('🎯 自动检测到品牌和产品名称页面，开始自动处理...');
                                // 这里需要传入productData，但在这个上下文中没有，所以我们需要从全局获取
                                const currentProductData = window.currentProductData || { name: '商品名称' };
                                await this.handleBrandAndProductPage(currentProductData);
                            } else {
                                console.log('ℹ️ 尚未进入品牌和产品名称页面，监听器将继续检测');
                            }

                            return true;
                        }

                        // 等待一段时间后再检查
                        await new Promise(resolve => setTimeout(resolve, checkInterval));
                        waitedTime += checkInterval;

                        if (waitedTime % 2000 === 0) {
                            console.log(`⏳ 继续等待"下一步"按钮可用... (${waitedTime/1000}s)`);

                            // 显示当前页面上的所有按钮信息
                            const allButtons = document.querySelectorAll('button');
                            const buttonInfo = Array.from(allButtons)
                                .filter(btn => btn.offsetWidth > 0 && btn.offsetHeight > 0)
                                .map(btn => `"${btn.textContent.trim()}" (disabled: ${btn.disabled})`)
                                .slice(0, 5); // 只显示前5个
                            console.log(`📋 当前可见按钮: ${buttonInfo.join(', ')}`);
                        }
                    }

                    console.warn('⚠️ 等待超时，"下一步"按钮未变为可用状态');
                    console.log('💡 提示：请手动点击页面上的"下一步"按钮进入品牌和产品名称页面');
                    return false;

                } catch (error) {
                    console.error('❌ 等待并点击"下一步"按钮失败:', error);
                    return false;
                }
            },

            // 查找可用的"下一步"按钮
            findEnabledNextStepButton() {
                try {
                    console.log('🔍 开始查找"下一步"按钮...');

                    // 抖音小店特定的"下一步"按钮选择器
                    const nextStepSelectors = [
                        '.style_nextStepWrapper__T_FGR button.ecom-g-btn-primary',
                        'button.style_nextStep__bDY8j',
                        '.style_nextStep__bDY8j',
                        'button[class*="nextStep"]',
                        'button[class*="next-step"]',
                        'button.ecom-g-btn-primary',
                        '.ecom-g-btn-primary'
                    ];

                    // 首先尝试特定选择器
                    for (const selector of nextStepSelectors) {
                        const button = document.querySelector(selector);
                        if (button) {
                            console.log(`📋 找到按钮 (${selector}): "${button.textContent.trim()}", disabled: ${button.disabled}, visible: ${button.offsetWidth > 0}`);
                            if (!button.disabled &&
                                button.offsetWidth > 0 && button.offsetHeight > 0 &&
                                button.textContent.includes('下一步')) {
                                console.log(`✅ 找到可用的"下一步"按钮: ${selector}`);
                                return button;
                            }
                        }
                    }

                    // 如果特定选择器没找到，查找所有包含"下一步"的可用按钮
                    console.log('🔍 在所有按钮中查找"下一步"...');
                    const allButtons = document.querySelectorAll('button');
                    console.log(`📋 页面总按钮数: ${allButtons.length}`);

                    let nextStepButtons = [];
                    for (const button of allButtons) {
                        if (button.textContent.includes('下一步')) {
                            nextStepButtons.push({
                                text: button.textContent.trim(),
                                disabled: button.disabled,
                                visible: button.offsetWidth > 0 && button.offsetHeight > 0,
                                className: button.className
                            });

                            if (!button.disabled &&
                                button.offsetWidth > 0 &&
                                button.offsetHeight > 0) {
                                console.log(`✅ 找到可用的"下一步"按钮: "${button.textContent.trim()}"`);
                                return button;
                            }
                        }
                    }

                    if (nextStepButtons.length > 0) {
                        console.log('📋 找到的"下一步"按钮:', nextStepButtons);
                    } else {
                        console.log('⚠️ 页面上没有找到包含"下一步"的按钮');

                        // 显示所有可见按钮的文本
                        const visibleButtons = Array.from(allButtons)
                            .filter(btn => btn.offsetWidth > 0 && btn.offsetHeight > 0)
                            .map(btn => `"${btn.textContent.trim()}" (disabled: ${btn.disabled})`)
                            .slice(0, 10); // 只显示前10个
                        console.log('📋 页面上的可见按钮:', visibleButtons);
                    }

                    return null;

                } catch (error) {
                    console.error('❌ 查找"下一步"按钮失败:', error);
                    return null;
                }
            },

            // 点击"下一步"按钮（暂时禁用，避免意外触发页面内容）
            async clickNextStepButton() {
                console.log('🔘 clickNextStepButton 方法已暂时禁用，避免自动点击"下一步"按钮');
                console.log('💡 提示：请手动点击页面上的"下一步"按钮继续流程');
                return false;
            },















            // 选择默认类目 - 智能推荐模式
            async selectDefaultCategory() {
                try {
                    console.log('🎯 选择默认类目（智能推荐模式）...');

                    // 使用通用的默认类目路径
                    const defaultCategoryPath = '日用品/纸品/生活用纸';

                    // 直接使用智能推荐查找类目
                    const recommendedCategory = await this.findRecommendedCategory(defaultCategoryPath);
                    if (recommendedCategory) {
                        console.log(`✅ 找到默认推荐类目: ${recommendedCategory.text}`);

                        // 解析推荐类目的层级路径
                        const categoryPath = this.parseCategoryPath(recommendedCategory.text);
                        console.log(`📋 默认类目路径: ${categoryPath.join(' → ')}`);

                        // 根据路径逐级选择类目
                        const selectResult = await this.selectCategoryByPath(categoryPath);
                        if (selectResult) {
                            console.log('✅ 默认类目选择完成');
                            return true;
                        }
                    }

                    console.warn('⚠️ 无法选择默认类目');
                    return false;

                } catch (error) {
                    console.error('❌ 选择默认类目失败:', error);
                    return false;
                }
            },
            
            // 测试连接
            testConnection() {
                const msToken = this.getMsToken();
                const csrfToken = this.getCsrfToken();
                return {
                    msToken: !!msToken,
                    csrfToken: !!csrfToken,
                    ready: !!(msToken && csrfToken)
                };
            },
            // 自动抓取所有类目的Schema数据
            async autoCaptureAllCategorySchemas() {
                console.log('🚀 开始自动抓取所有类目Schema...');

                try {
                    // 1. 获取所有已开通的类目
                    console.log('📋 正在获取所有已开通类目...');
                    const allCategories = await this.getAllOpenedCategories();

                    if (allCategories.length === 0) {
                        console.error('❌ 未找到任何已开通的类目');
                        alert('未找到任何已开通的类目，请确保页面已正确加载');
                        return;
                    }

                    console.log(`📊 找到 ${allCategories.length} 个叶子节点类目，开始抓取Schema...`);

                    let successCount = 0;
                    let failedCount = 0;
                    const errors = [];

                    // 2. 按三级类目分组处理四级类目
                    const processedLevel3Categories = new Set();
                    const level4Categories = allCategories.filter(cat => cat.level === 4);
                    const nonLevel4Categories = allCategories.filter(cat => cat.level !== 4);

                    // 先处理四级类目（按三级类目分组）
                    if (level4Categories.length > 0) {
                        console.log(`📂 发现 ${level4Categories.length} 个四级类目，按三级类目分组处理...`);

                        for (const category of level4Categories) {
                            const level3Path = category.path.slice(0, 3).join(' > ');

                            if (!processedLevel3Categories.has(level3Path)) {
                                processedLevel3Categories.add(level3Path);

                                console.log(`🎯 处理三级类目下的所有四级类目: ${level3Path}`);

                                try {
                                    const success = await this.captureSchemaForFourthLevelCategories(category.path);

                                    if (success) {
                                        // 统计这个三级类目下的四级类目数量
                                        const relatedLevel4Count = level4Categories.filter(cat =>
                                            cat.path.slice(0, 3).join(' > ') === level3Path
                                        ).length;

                                        successCount += relatedLevel4Count;
                                        console.log(`✅ 三级类目 "${level3Path}" 下的 ${relatedLevel4Count} 个四级类目处理完成`);
                                    } else {
                                        const relatedLevel4Count = level4Categories.filter(cat =>
                                            cat.path.slice(0, 3).join(' > ') === level3Path
                                        ).length;

                                        failedCount += relatedLevel4Count;
                                        console.error(`❌ 三级类目 "${level3Path}" 下的 ${relatedLevel4Count} 个四级类目处理失败`);
                                    }
                                } catch (error) {
                                    const relatedLevel4Count = level4Categories.filter(cat =>
                                        cat.path.slice(0, 3).join(' > ') === level3Path
                                    ).length;

                                    failedCount += relatedLevel4Count;
                                    console.error(`❌ 处理三级类目 "${level3Path}" 的四级类目时发生错误:`, error);
                                }

                                // 添加延迟
                                await this.delay(2000);
                            }
                        }
                    }

                    // 3. 处理非四级类目
                    for (let i = 0; i < nonLevel4Categories.length; i++) {
                        const category = nonLevel4Categories[i];
                        const current = i + 1;
                        const total = nonLevel4Categories.length;

                        console.log(`📝 [${current}/${total}] 处理类目: ${category.fullPath}`);

                        try {

                            // 4. 常规类目处理：选择类目
                            console.log(`🎯 选择类目: ${category.fullPath}`);
                            const selectSuccess = await this.selectCategoryByPath(category.path);

                            if (!selectSuccess) {
                                throw new Error('选择类目失败');
                            }

                            // 5. 等待页面响应和类目切换完成
                            console.log('⏳ 等待页面响应和类目切换完成...');
                            await this.delay(4000); // 增加到4秒，确保页面完全响应和稳定

                            // 6. 监听getSchema响应
                            console.log('👂 开始监听getSchema API响应...');
                            const schema = await this.waitForGetSchemaResponse(20000); // 增加到20秒超时

                            if (schema && schema.errno === 0) {
                                // 6. 构建保存数据
                                const saveData = {
                                    timestamp: new Date().toISOString(),
                                    category_info: {
                                        path: category.path,
                                        fullPath: category.fullPath,
                                        level: category.level,
                                        name: category.name
                                    },
                                    schema_data: schema,
                                    generated_by: 'auto-capture-v1'
                                };

                                // 7. 保存到文件（保存到最后一级类目文件夹下）
                                const categoryFolder = category.path.slice(0, -1).join('/'); // 除了最后一级的路径
                                const lastLevelName = category.path[category.path.length - 1]; // 最后一级类目名
                                const fileName = categoryFolder ?
                                    `categories/${categoryFolder}/${lastLevelName}必填项.json` :
                                    `categories/${lastLevelName}必填项.json`;

                                console.log(`💾 保存文件: ${fileName}`);
                                this.saveSchemaToFile(saveData, fileName);

                                // 8. 保存到Chrome存储
                                const storageKey = `schema_${category.path.join('_')}`;
                                try {
                                    await chrome.storage.local.set({ [storageKey]: saveData });
                                } catch (storageError) {
                                    console.warn('⚠️ Chrome存储保存失败:', storageError);
                                }

                                successCount++;
                                console.log(`✅ [${current}/${total}] 成功: ${category.fullPath}`);

                            } else {
                                throw new Error(`Schema数据无效: ${schema?.msg || schema?.errmsg || '未知错误'}`);
                            }

                        } catch (error) {
                            failedCount++;
                            const errorMsg = `${category.fullPath}: ${error.message}`;
                            errors.push(errorMsg);
                            console.error(`❌ [${current}/${total}] 失败: ${errorMsg}`);
                        }

                        // 9. 添加延迟避免请求过快，确保系统稳定
                        if (i < nonLevel4Categories.length - 1) {
                            console.log('⏳ 等待系统稳定，准备处理下一个类目...');
                            await this.delay(4000); // 增加到4秒，确保系统充分稳定
                        }
                    }

                    // 10. 显示完成结果
                    const totalProcessed = successCount + failedCount;
                    const successRate = totalProcessed > 0 ? ((successCount / totalProcessed) * 100).toFixed(1) : '0.0';
                    const message = `🎉 Schema抓取完成！\n\n📊 统计信息:\n- 总类目数: ${allCategories.length}\n- 处理数: ${totalProcessed} 个\n- 成功: ${successCount} 个\n- 失败: ${failedCount} 个\n- 成功率: ${successRate}%\n\n📁 文件已保存到categories目录`;

                    console.log(`🎉 所有类目Schema抓取完成！成功 ${successCount}/${totalProcessed} 个 (${successRate}%)`);

                    if (errors.length > 0) {
                        console.log('❌ 失败的类目:');
                        errors.forEach(error => console.log(`   - ${error}`));
                    }

                    alert(message);

                } catch (error) {
                    console.error('💥 自动抓取Schema失败:', error);
                    alert(`抓取失败: ${error.message}`);
                }
            },
            // 获取所有已开通的类目（从页面直接遍历）
            async getAllOpenedCategories(maxLevel1Categories = null) {
                const results = [];
                const categoryContainer = document.querySelector('.style_cat1__GxbQb');
                if (!categoryContainer) {
                    console.error('❌ 未找到类目选择器');
                    return results;
                }

                console.log('🔍 开始获取所有已开通类目...');

                // 获取所有一级类目
                const level1Items = categoryContainer.querySelectorAll('.style_catItem__gjkVg');
                const totalLevel1 = level1Items.length;
                const processLevel1 = maxLevel1Categories ? Math.min(maxLevel1Categories, totalLevel1) : totalLevel1;

                console.log(`📋 找到 ${totalLevel1} 个一级类目${maxLevel1Categories ? `，限制处理前 ${processLevel1} 个` : ''}`);

                for (let i = 0; i < processLevel1; i++) {
                    const item = level1Items[i];
                    const nameEl = item.querySelector('.style_name__F9zo3 span');
                    if (!nameEl) continue;

                    const level1Name = nameEl.textContent.trim();
                    console.log(`📂 处理一级类目: ${level1Name} (${i + 1}/${processLevel1})`);

                    try {
                        // 点击一级类目
                        console.log(`🖱️ 点击一级类目: ${level1Name}`);
                        item.click();

                        // 等待一级类目响应和二级类目加载
                        console.log('⏳ 等待一级类目响应...');
                        await this.delay(2500); // 增加到2.5秒确保二级类目充分加载

                        // 获取二级类目容器
                        const level2Container = document.querySelector('.style_cat2__j3Rto');
                        if (level2Container) {
                            await this.processLevel2Categories(level2Container, [level1Name], results);
                        }

                    } catch (error) {
                        console.error(`❌ 处理一级类目 ${level1Name} 失败:`, error);
                    }
                }

                console.log(`✅ 获取完成，共找到 ${results.length} 个叶子节点类目`);
                return results;
            },

            // 处理二级类目
            async processLevel2Categories(container, path, results) {
                const level2Items = container.querySelectorAll('.style_catItem__gjkVg');

                for (let i = 0; i < level2Items.length; i++) {
                    const item = level2Items[i];
                    const nameEl = item.querySelector('.style_name__F9zo3 span');
                    if (!nameEl) continue;

                    const level2Name = nameEl.textContent.trim();
                    const currentPath = [...path, level2Name];

                    try {
                        // 在点击二级类目之前开始监听（可能是叶子节点）
                        console.log('👂 预先开始监听getSchema API响应...');
                        const schemaPromise = this.waitForGetSchemaResponse(8000);

                        // 点击二级类目
                        console.log(`🖱️ 点击二级类目: ${level2Name}`);
                        item.click();

                        // 等待二级类目响应和三级类目加载
                        console.log('⏳ 等待二级类目响应...');
                        await this.delay(2000); // 增加到2秒确保三级类目充分加载

                        // 获取三级类目容器
                        const level3Container = document.querySelector('.style_cat3__afwud');
                        if (level3Container && level3Container.children.length > 0) {
                            await this.processLevel3Categories(level3Container, currentPath, results);
                        } else {
                            // 二级类目就是叶子节点，需要获取Schema
                            console.log(`📋 二级叶子节点: ${currentPath.join(' > ')}`);

                            try {
                                // 使用预先开始的Schema监听
                                console.log('⏳ 等待预先监听的getSchema响应...');
                                const schema = await schemaPromise;

                                if (schema && schema.errno === 0) {
                                    // 构建保存数据
                                    const saveData = {
                                        timestamp: new Date().toISOString(),
                                        category_info: {
                                            path: currentPath,
                                            fullPath: currentPath.join(' > '),
                                            level: 2,
                                            name: level2Name
                                        },
                                        schema_data: schema,
                                        generated_by: 'auto-capture-v3'
                                    };

                                    // 保存到文件（使用安全路径）
                                    const lastLevelName = currentPath[currentPath.length - 1];
                                    const fileName = this.buildSafeFilePath(currentPath, `${lastLevelName}必填项.json`);

                                    console.log(`💾 保存文件: ${fileName}`);
                                    this.saveSchemaToFile(saveData, fileName);

                                    console.log(`✅ 二级类目Schema获取成功: ${currentPath.join(' > ')}`);
                                } else {
                                    console.error(`❌ 二级类目Schema获取失败: ${currentPath.join(' > ')}`);
                                }
                            } catch (error) {
                                console.error(`❌ 处理二级类目Schema ${level2Name} 失败:`, error);
                            }

                            results.push({
                                path: currentPath,
                                name: level2Name,
                                fullPath: currentPath.join(' > '),
                                level: 2
                            });
                        }

                    } catch (error) {
                        console.error(`❌ 处理二级类目 ${level2Name} 失败:`, error);
                    }
                }
            },

            // 处理三级类目
            async processLevel3Categories(container, path, results) {
                const level3Items = container.querySelectorAll('.style_catItem__gjkVg');

                for (let i = 0; i < level3Items.length; i++) {
                    const item = level3Items[i];
                    const nameEl = item.querySelector('.style_name__F9zo3 span');
                    if (!nameEl) continue;

                    const level3Name = nameEl.textContent.trim();
                    const currentPath = [...path, level3Name];

                    try {
                        // 在点击三级类目之前开始监听（可能是叶子节点）
                        console.log('👂 预先开始监听getSchema API响应...');
                        const schemaPromise = this.waitForGetSchemaResponse(8000);

                        // 点击三级类目
                        console.log(`🖱️ 点击三级类目: ${level3Name}`);
                        item.click();

                        // 等待三级类目响应和四级类目加载
                        console.log('⏳ 等待三级类目响应...');
                        await this.delay(1800); // 增加到1.8秒确保四级类目充分加载

                        // 检查是否有四级类目
                        await this.delay(500); // 等待四级类目容器加载
                        const level4Container = document.querySelectorAll('.style_cat3__afwud')[1];

                        if (level4Container && level4Container.children.length > 0) {
                            console.log(`🔍 检测到四级类目容器，包含 ${level4Container.children.length} 个子元素`);
                            await this.processLevel4Categories(level4Container, currentPath, results);
                        } else {
                            // 尝试其他可能的四级类目选择器
                            const alternativeSelectors = [
                                '.overflow-scoll_overflowScroll__igXmX:last-child',
                                '.style_cat3__afwud:last-child',
                                '.style_selectTree__McRTS > div:nth-child(4)'
                            ];

                            let found4thLevel = false;
                            for (const selector of alternativeSelectors) {
                                const container = document.querySelector(selector);
                                if (container && container.children.length > 0) {
                                    console.log(`🔍 通过备用选择器找到四级类目: ${selector}`);
                                    await this.processLevel4Categories(container, currentPath, results);
                                    found4thLevel = true;
                                    break;
                                }
                            }

                            if (!found4thLevel) {
                                // 三级类目就是叶子节点，需要获取Schema
                                console.log(`📋 三级叶子节点: ${currentPath.join(' > ')}`);

                                try {
                                    // 使用预先开始的Schema监听
                                    console.log('⏳ 等待预先监听的getSchema响应...');
                                    const schema = await schemaPromise;

                                    if (schema && schema.errno === 0) {
                                        // 构建保存数据
                                        const saveData = {
                                            timestamp: new Date().toISOString(),
                                            category_info: {
                                                path: currentPath,
                                                fullPath: currentPath.join(' > '),
                                                level: 3,
                                                name: level3Name
                                            },
                                            schema_data: schema,
                                            generated_by: 'auto-capture-v3'
                                        };

                                        // 保存到文件（使用安全路径）
                                        const lastLevelName = currentPath[currentPath.length - 1];
                                        const fileName = this.buildSafeFilePath(currentPath, `${lastLevelName}必填项.json`);

                                        console.log(`💾 保存文件: ${fileName}`);
                                        this.saveSchemaToFile(saveData, fileName);

                                        console.log(`✅ 三级类目Schema获取成功: ${currentPath.join(' > ')}`);
                                    } else {
                                        console.error(`❌ 三级类目Schema获取失败: ${currentPath.join(' > ')}`);
                                    }
                                } catch (error) {
                                    console.error(`❌ 处理三级类目Schema ${level3Name} 失败:`, error);
                                }

                                results.push({
                                    path: currentPath,
                                    name: level3Name,
                                    fullPath: currentPath.join(' > '),
                                    level: 3
                                });
                            }
                        }

                    } catch (error) {
                        console.error(`❌ 处理三级类目 ${level3Name} 失败:`, error);
                    }
                }
            },

            // 处理四级类目（叶子节点）- 实际点击每个四级类目并获取Schema
            async processLevel4Categories(container, path, results) {
                const level4Items = container.querySelectorAll('.style_catItem__gjkVg');
                console.log(`📂 找到 ${level4Items.length} 个四级类目`);

                for (let i = 0; i < level4Items.length; i++) {
                    const item = level4Items[i];
                    const nameEl = item.querySelector('.style_name__F9zo3 span');
                    if (!nameEl) continue;

                    const level4Name = nameEl.textContent.trim();
                    const currentPath = [...path, level4Name];

                    try {
                        // 获取上一级类目ID（这是API请求中实际传递的ID）
                        const parentCategoryId = this.getParentCategoryId(currentPath);

                        if (!parentCategoryId) {
                            console.warn(`⚠️ 无法获取类目 ${currentPath.join(' > ')} 的上一级ID，跳过`);
                            continue;
                        }

                        console.log(`⚠️ 全局监听线程已禁用，跳过类目: ${currentPath.join(' > ')}`);

                        // 点击四级类目（仅用于UI展示）
                        console.log(`🖱️ 点击四级类目: ${level4Name} (${i + 1}/${level4Items.length})`);
                        item.click();

                        // 等待一小段时间让点击生效
                        await this.delay(800);

                        // 全局监听线程已禁用，跳过Schema获取
                        console.log('⚠️ Schema获取功能已禁用');
                        const saveData = null;

                        if (false) { // 禁用此分支
                            console.log(`✅ 四级类目Schema获取成功: ${currentPath.join(' > ')}`);
                            console.log(`📊 Schema字段数量: ${saveData.schema_data?.data?.fields?.length || 0}`);
                        } else {
                            throw new Error('全局监听线程未返回有效数据');
                        }

                        // 添加延迟避免请求过快
                        if (i < level4Items.length - 1) {
                            console.log('⏳ 等待系统稳定，准备处理下一个四级类目...');
                            await this.delay(3000);
                        }

                    } catch (error) {
                        console.error(`❌ 处理四级类目 ${level4Name} 失败:`, error);
                    }

                    // 将四级类目添加到结果中（用于统计）
                    results.push({
                        path: currentPath,
                        name: level4Name,
                        fullPath: currentPath.join(' > '),
                        level: 4
                    });
                }
            },

            // 获取当前页面已选择的类目路径
            getCurrentSelectedCategoryPath() {
                const path = [];

                try {
                    // 检查一级类目
                    const level1Container = document.querySelector('.style_cat1__GxbQb');
                    if (level1Container) {
                        const selectedLevel1 = level1Container.querySelector('.style_catItem__gjkVg.style_active__Ej8Qs, .style_catItem__gjkVg[class*="active"]');
                        if (selectedLevel1) {
                            const nameEl = selectedLevel1.querySelector('.style_name__F9zo3 span');
                            if (nameEl) {
                                path.push(nameEl.textContent.trim());
                            }
                        }
                    }

                    // 检查二级类目
                    const level2Container = document.querySelector('.style_cat2__j3Rto');
                    if (level2Container && path.length > 0) {
                        const selectedLevel2 = level2Container.querySelector('.style_catItem__gjkVg.style_active__Ej8Qs, .style_catItem__gjkVg[class*="active"]');
                        if (selectedLevel2) {
                            const nameEl = selectedLevel2.querySelector('.style_name__F9zo3 span');
                            if (nameEl) {
                                path.push(nameEl.textContent.trim());
                            }
                        }
                    }

                    // 检查三级类目
                    const level3Container = document.querySelector('.style_cat3__afwud');
                    if (level3Container && path.length > 1) {
                        const selectedLevel3 = level3Container.querySelector('.style_catItem__gjkVg.style_active__Ej8Qs, .style_catItem__gjkVg[class*="active"]');
                        if (selectedLevel3) {
                            const nameEl = selectedLevel3.querySelector('.style_name__F9zo3 span');
                            if (nameEl) {
                                path.push(nameEl.textContent.trim());
                            }
                        }
                    }

                    // 检查四级类目
                    const level4Selectors = [
                        '.style_cat3__afwud:nth-child(2)',
                        '.overflow-scoll_overflowScroll__igXmX:last-child'
                    ];

                    if (path.length > 2) {
                        for (const selector of level4Selectors) {
                            const level4Container = document.querySelector(selector);
                            if (level4Container) {
                                const selectedLevel4 = level4Container.querySelector('.style_catItem__gjkVg.style_active__Ej8Qs, .style_catItem__gjkVg[class*="active"]');
                                if (selectedLevel4) {
                                    const nameEl = selectedLevel4.querySelector('.style_name__F9zo3 span');
                                    if (nameEl) {
                                        path.push(nameEl.textContent.trim());
                                        break;
                                    }
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.error('❌ 获取当前选择类目路径失败:', error);
                }

                return path;
            },

            // 处理单个类目的Schema获取
            async processSingleCategorySchema(categoryPath) {
                console.log(`🎯 开始处理单个类目Schema: ${categoryPath.join(' > ')}`);

                try {
                    // 检查是否为四级类目
                    if (categoryPath.length === 4) {
                        console.log('🔍 检测到四级类目，使用专门处理方法');

                        // 对于四级类目，处理同一三级类目下的所有四级类目
                        const success = await this.captureSchemaForFourthLevelCategories(categoryPath);

                        if (success) {
                            return {
                                success: true,
                                processedCount: 1,
                                successCount: 1,
                                failedCount: 0,
                                message: '四级类目Schema获取成功'
                            };
                        } else {
                            return {
                                success: false,
                                error: '四级类目Schema获取失败'
                            };
                        }
                    } else {
                        // 处理非四级类目
                        console.log('⏳ 等待页面响应和类目切换完成...');
                        await this.delay(2000);

                        console.log('👂 开始监听getSchema API响应...');
                        const schema = await this.waitForGetSchemaResponse(20000);

                        if (schema && schema.errno === 0) {
                            // 构建保存数据
                            const saveData = {
                                timestamp: new Date().toISOString(),
                                category_info: {
                                    path: categoryPath,
                                    fullPath: categoryPath.join(' > '),
                                    level: categoryPath.length,
                                    name: categoryPath[categoryPath.length - 1]
                                },
                                schema_data: schema,
                                generated_by: 'single-category-v1'
                            };

                            // 保存到文件
                            const categoryFolder = categoryPath.slice(0, -1).join('/');
                            const lastLevelName = categoryPath[categoryPath.length - 1];
                            const fileName = categoryFolder ?
                                `categories/${categoryFolder}/${lastLevelName}必填项.json` :
                                `categories/${lastLevelName}必填项.json`;

                            console.log(`💾 保存文件: ${fileName}`);
                            this.saveSchemaToFile(saveData, fileName);

                            console.log(`✅ 单个类目Schema获取成功: ${categoryPath.join(' > ')}`);

                            return {
                                success: true,
                                processedCount: 1,
                                successCount: 1,
                                failedCount: 0,
                                message: `Schema获取成功: ${categoryPath.join(' > ')}`
                            };
                        } else {
                            console.error(`❌ Schema获取失败: ${schema?.msg || schema?.errmsg || '未知错误'}`);
                            return {
                                success: false,
                                error: `Schema获取失败: ${schema?.msg || schema?.errmsg || '未知错误'}`
                            };
                        }
                    }
                } catch (error) {
                    console.error('❌ 单个类目Schema处理失败:', error);
                    return {
                        success: false,
                        error: error.message
                    };
                }
            },

            // 专门用于Schema抓取的四级类目处理方法
            async captureSchemaForFourthLevelCategories(categoryPath) {
                console.log(`🎯 开始处理四级类目Schema抓取: ${categoryPath.slice(0, 3).join(' > ')}`);

                try {
                    // 1. 选择到三级类目
                    const level3Path = categoryPath.slice(0, 3);
                    const selectSuccess = await this.selectCategoryByPath(level3Path);

                    if (!selectSuccess) {
                        console.error(`❌ 选择三级类目失败: ${level3Path.join(' > ')}`);
                        return false;
                    }

                    // 2. 等待四级类目加载
                    console.log('⏳ 等待四级类目加载...');
                    await this.delay(3000);

                    // 3. 查找四级类目容器
                    const level4Selectors = [
                        '.style_cat3__afwud:nth-child(2)',
                        '.overflow-scoll_overflowScroll__igXmX:last-child',
                        '.style_cat3__afwud:last-child'
                    ];

                    let level4Container = null;
                    for (const selector of level4Selectors) {
                        const container = document.querySelector(selector);
                        if (container && container.children.length > 0) {
                            const items = container.querySelectorAll('.style_catItem__gjkVg');
                            if (items.length > 0) {
                                level4Container = container;
                                console.log(`✅ 找到四级类目容器: ${selector} (${items.length} 个选项)`);
                                break;
                            }
                        }
                    }

                    if (!level4Container) {
                        console.error('❌ 未找到四级类目容器');
                        return false;
                    }

                    // 4. 处理每个四级类目
                    const level4Items = level4Container.querySelectorAll('.style_catItem__gjkVg');
                    console.log(`📂 开始处理 ${level4Items.length} 个四级类目`);

                    let successCount = 0;
                    let failedCount = 0;

                    for (let i = 0; i < level4Items.length; i++) {
                        const item = level4Items[i];
                        const nameEl = item.querySelector('.style_name__F9zo3 span');
                        if (!nameEl) continue;

                        const level4Name = nameEl.textContent.trim();
                        const fullPath = [...level3Path, level4Name];

                        try {
                            console.log(`🖱️ [${i + 1}/${level4Items.length}] 点击四级类目: ${level4Name}`);
                            item.click();

                            // 等待四级类目响应
                            console.log('⏳ 等待四级类目响应...');
                            await this.delay(2000);

                            // 监听getSchema响应
                            console.log('👂 开始监听getSchema API响应...');
                            const schema = await this.waitForGetSchemaResponse(20000);

                            if (schema && schema.errno === 0) {
                                // 构建保存数据
                                const saveData = {
                                    timestamp: new Date().toISOString(),
                                    category_info: {
                                        path: fullPath,
                                        fullPath: fullPath.join(' > '),
                                        level: 4,
                                        name: level4Name
                                    },
                                    schema_data: schema,
                                    generated_by: 'auto-capture-v2'
                                };

                                // 保存到文件
                                const categoryFolder = fullPath.slice(0, -1).join('/');
                                const lastLevelName = fullPath[fullPath.length - 1];
                                const fileName = categoryFolder ?
                                    `categories/${categoryFolder}/${lastLevelName}必填项.json` :
                                    `categories/${lastLevelName}必填项.json`;

                                console.log(`💾 保存文件: ${fileName}`);
                                this.saveSchemaToFile(saveData, fileName);

                                successCount++;
                                console.log(`✅ [${i + 1}/${level4Items.length}] 四级类目Schema获取成功: ${fullPath.join(' > ')}`);
                            } else {
                                failedCount++;
                                console.error(`❌ [${i + 1}/${level4Items.length}] 四级类目Schema获取失败: ${fullPath.join(' > ')}`);
                            }

                            // 添加延迟避免请求过快
                            if (i < level4Items.length - 1) {
                                console.log('⏳ 等待系统稳定，准备处理下一个四级类目...');
                                await this.delay(3000);
                            }

                        } catch (error) {
                            failedCount++;
                            console.error(`❌ [${i + 1}/${level4Items.length}] 处理四级类目 ${level4Name} 失败:`, error);
                        }
                    }

                    console.log(`🎉 四级类目处理完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);
                    return successCount > 0;

                } catch (error) {
                    console.error('❌ 四级类目Schema抓取失败:', error);
                    return false;
                }
            },

            // 选择指定的类目路径（修正递归调用问题）
            async selectCategoryByPath(pathArray) {
                if (!pathArray || pathArray.length === 0) {
                    console.error('❌ 类目路径为空');
                    return false;
                }

                console.log(`🎯 选择类目路径: ${pathArray.join(' > ')}`);

                try {
                    // 选择一级类目
                    const level1Container = document.querySelector('.style_cat1__GxbQb');
                    if (!level1Container) {
                        console.error('❌ 未找到一级类目容器');
                        return false;
                    }

                    const level1Item = this.findCategoryItem(level1Container, pathArray[0]);
                    if (!level1Item) {
                        console.error(`❌ 未找到一级类目: ${pathArray[0]}`);
                        return false;
                    }

                    level1Item.click();
                    console.log('⏳ 等待一级类目响应...');
                    await this.delay(2500); // 增加到2.5秒确保二级类目充分加载

                    if (pathArray.length === 1) return true;

                    // 选择二级类目
                    const level2Container = document.querySelector('.style_cat2__j3Rto');
                    if (!level2Container) {
                        console.error('❌ 未找到二级类目容器');
                        return false;
                    }

                    const level2Item = this.findCategoryItem(level2Container, pathArray[1]);
                    if (!level2Item) {
                        console.error(`❌ 未找到二级类目: ${pathArray[1]}`);
                        return false;
                    }

                    level2Item.click();
                    console.log('⏳ 等待二级类目响应...');
                    await this.delay(2000); // 增加到2秒确保三级类目充分加载

                    if (pathArray.length === 2) return true;

                    // 选择三级类目
                    const level3Container = document.querySelector('.style_cat3__afwud');
                    if (!level3Container) {
                        console.error('❌ 未找到三级类目容器');
                        return false;
                    }

                    const level3Item = this.findCategoryItem(level3Container, pathArray[2]);
                    if (!level3Item) {
                        console.error(`❌ 未找到三级类目: ${pathArray[2]}`);
                        return false;
                    }

                    level3Item.click();
                    console.log('⏳ 等待三级类目响应...');
                    await this.delay(1800); // 增加到1.8秒确保四级类目充分加载

                    if (pathArray.length === 3) return true;

                    // 选择四级类目
                    console.log(`🔍 开始查找四级类目: ${pathArray[3]}`);

                    // 尝试多种四级类目容器选择器
                    const level4Selectors = [
                        '.style_cat3__afwud:nth-child(2)', // 第二个style_cat3__afwud容器
                        '.overflow-scoll_overflowScroll__igXmX:last-child', // 最后一个滚动容器
                        '.style_cat3__afwud:last-child', // 最后一个类目容器
                        '.style_selectTree__McRTS > div:nth-child(4)' // 第四个类目树容器
                    ];

                    let level4Container = null;
                    let level4Item = null;

                    for (const selector of level4Selectors) {
                        const container = document.querySelector(selector);
                        if (container && container.children.length > 0) {
                            console.log(`🔍 尝试四级类目容器: ${selector} (包含 ${container.children.length} 个子元素)`);
                            const item = this.findCategoryItem(container, pathArray[3]);
                            if (item) {
                                level4Container = container;
                                level4Item = item;
                                console.log(`✅ 在容器 ${selector} 中找到四级类目: ${pathArray[3]}`);
                                break;
                            }
                        }
                    }

                    // 如果还没找到，尝试备用方法
                    if (!level4Item) {
                        console.log('🔍 尝试备用方法查找四级类目...');
                        const allContainers = document.querySelectorAll('.style_cat3__afwud');
                        console.log(`📊 找到 ${allContainers.length} 个 .style_cat3__afwud 容器`);

                        for (let i = 0; i < allContainers.length; i++) {
                            const container = allContainers[i];
                            if (container.children.length > 0) {
                                console.log(`🔍 检查容器 ${i}: ${container.children.length} 个子元素`);
                                const item = this.findCategoryItem(container, pathArray[3]);
                                if (item) {
                                    level4Container = container;
                                    level4Item = item;
                                    console.log(`✅ 在容器 ${i} 中找到四级类目: ${pathArray[3]}`);
                                    break;
                                }
                            }
                        }
                    }

                    if (!level4Item) {
                        console.error(`❌ 未找到四级类目: ${pathArray[3]}`);
                        console.log('🔍 调试信息: 当前页面的四级类目选项:');

                        // 输出调试信息
                        const allContainers = document.querySelectorAll('.style_cat3__afwud, .overflow-scoll_overflowScroll__igXmX');
                        allContainers.forEach((container, index) => {
                            const items = container.querySelectorAll('.style_catItem__gjkVg .style_name__F9zo3 span');
                            if (items.length > 0) {
                                const names = Array.from(items).map(item => item.textContent.trim());
                                console.log(`   容器 ${index}: [${names.join(', ')}]`);
                            }
                        });

                        return false;
                    }

                    level4Item.click();
                    console.log(`✅ 成功点击四级类目: ${pathArray[3]}`);
                    console.log('⏳ 等待四级类目响应...');
                    await this.delay(1500); // 增加到1.5秒确保四级类目充分响应

                    return true;

                } catch (error) {
                    console.error('❌ 选择类目路径失败:', error);
                    return false;
                }
            },

            // 在容器中查找指定名称的类目项
            findCategoryItem(container, categoryName) {
                const items = container.querySelectorAll('.style_catItem__gjkVg');
                for (const item of items) {
                    const nameEl = item.querySelector('.style_name__F9zo3 span');
                    if (nameEl && nameEl.textContent.trim() === categoryName) {
                        return item;
                    }
                }
                return null;
            },
            // 监听并等待 getSchema 响应（已启用网络监听）
            async waitForGetSchemaResponse(timeout = 20000, expectedCategoryId = null) {
                console.log('🔍 等待getSchema响应...');

                return new Promise((resolve) => {
                    const startTime = Date.now();

                    const checkResponse = () => {
                        if (expectedCategoryId && this.capturedData.has(expectedCategoryId)) {
                            const data = this.capturedData.get(expectedCategoryId);
                            console.log(`✅ 获取到类目 ${expectedCategoryId} 的响应`);
                            resolve(data.response);
                            return;
                        }

                        // 如果没有指定类目ID，检查是否有任何新的响应
                        if (!expectedCategoryId && this.capturedData.size > 0) {
                            const latestData = Array.from(this.capturedData.values()).pop();
                            console.log('✅ 获取到最新的getSchema响应');
                            resolve(latestData.response);
                            return;
                        }

                        // 检查是否超时
                        if (Date.now() - startTime > timeout) {
                            console.log('⏰ 等待getSchema响应超时');
                            resolve(null);
                            return;
                        }

                        // 继续检查
                        setTimeout(checkResponse, 500);
                    };

                    checkResponse();
                });








            },

            // 尝试从页面获取Schema数据（备用方案）
            tryGetSchemaFromPage() {
                try {
                    // 方法1: 检查页面上是否有表单字段
                    const formElements = document.querySelectorAll('input, select, textarea');
                    if (formElements.length > 0) {
                        console.log(`🔍 页面检测到 ${formElements.length} 个表单元素`);

                        // 构建简单的Schema结构
                        const fields = [];
                        formElements.forEach((element, index) => {
                            const label = this.getFieldLabel(element);
                            const required = element.hasAttribute('required') ||
                                           element.getAttribute('aria-required') === 'true' ||
                                           element.closest('.required') !== null;

                            if (label) {
                                fields.push({
                                    id: element.id || `field_${index}`,
                                    name: element.name || label,
                                    label: label,
                                    type: element.type || element.tagName.toLowerCase(),
                                    required: required,
                                    element_type: element.tagName.toLowerCase()
                                });
                            }
                        });

                        if (fields.length > 0) {
                            return {
                                errno: 0,
                                data: {
                                    fields: fields,
                                    source: 'page_extraction',
                                    extracted_at: new Date().toISOString()
                                },
                                msg: 'success'
                            };
                        }
                    }

                    // 方法2: 检查是否有React或Vue组件数据
                    const reactElements = document.querySelectorAll('[data-reactroot], [data-react-class]');
                    if (reactElements.length > 0) {
                        console.log('🔍 检测到React组件，尝试获取组件数据');
                        // 这里可以添加更复杂的React数据提取逻辑
                    }

                    return null;
                } catch (error) {
                    console.error('❌ 从页面获取Schema数据失败:', error);
                    return null;
                }
            },

            // 获取表单字段的标签
            getFieldLabel(element) {
                try {
                    // 方法1: 通过label标签
                    if (element.id) {
                        const label = document.querySelector(`label[for="${element.id}"]`);
                        if (label) return label.textContent.trim();
                    }

                    // 方法2: 通过父级元素查找
                    const parent = element.closest('.form-item, .field, .input-group, [class*="form"]');
                    if (parent) {
                        const label = parent.querySelector('label, .label, [class*="label"]');
                        if (label) return label.textContent.trim();
                    }

                    // 方法3: 通过placeholder
                    if (element.placeholder) {
                        return element.placeholder;
                    }

                    // 方法4: 通过aria-label
                    if (element.getAttribute('aria-label')) {
                        return element.getAttribute('aria-label');
                    }

                    return null;
                } catch (error) {
                    return null;
                }
            },

            // 全局Schema监听线程管理器（已禁用）
            schemaListenerManager: {
                isRunning: false,
                pendingRequests: new Map(),
                capturedResponses: new Map(),
                originalFetch: null,
                originalXHROpen: null,
                originalXHRSend: null,

                // 启动全局监听线程（已禁用）
                start() {
                    console.log('⚠️ 全局Schema监听线程已禁用');
                    return;
                },

                // 停止监听线程（已禁用）
                stop() {
                    console.log('⚠️ 全局Schema监听线程已禁用');
                    return;
                },

                // 通过消息机制请求Schema数据（已禁用）
                async requestSchema(categoryId, categoryPath, timeout = 30000) {
                    console.log('⚠️ Schema请求功能已禁用');
                    return Promise.resolve(null);
                },

                // 兼容旧接口（已启用）
                registerCategoryRequest(categoryId, categoryPath, timeout = 15000) {
                    console.log(`🔧 注册类目请求监听: ${categoryId} - ${categoryPath}`);
                    // 清除之前的数据
                    this.capturedData.delete(categoryId);
                    return Promise.resolve(true);
                },

                // 设置全局监听器（已启用）
                setupGlobalListener() {
                    console.log('🔧 设置全局Schema API监听器');
                    this.setupNetworkListener();
                    return;
                },

                // 处理Schema响应（已启用）
                handleSchemaResponse(data, requestBody) {
                    console.log('📦 处理Schema响应数据');
                    try {
                        const categoryId = this.extractCategoryIdFromRequest({ body: requestBody });
                        if (categoryId && data) {
                            this.capturedData.set(categoryId, {
                                response: data,
                                timestamp: new Date().toISOString()
                            });
                            console.log(`💾 保存类目 ${categoryId} 的响应数据`);
                        }
                    } catch (error) {
                        console.error('❌ 处理Schema响应失败:', error);
                    }
                    return;
                },
                // 恢复网络方法（已启用）
                restoreNetworkMethods() {
                    console.log('🔄 恢复网络方法');
                    this.clearNetworkListener();
                    return;
                },

                // 处理fetch请求（已启用）
                async handleFetchRequest(args) {
                    console.log('🌐 处理fetch请求');
                    // 这个方法现在在setupNetworkListener中实现
                    return null;
                },

                // 处理XHR响应（已禁用）
                handleXHRResponse(xhr) {
                    console.log('⚠️ XHR响应处理功能已禁用');
                    return;
                },

                // 向请求体注入类目ID（已禁用）
                injectCategoryIdToBody(body) {
                    console.log('⚠️ 类目ID注入功能已禁用');
                    return body;
                },

                // 从请求体中提取类目ID（已禁用）
                extractCategoryIdFromBody(body) {
                    console.log('⚠️ 类目ID提取功能已禁用');
                    return null;
                    return null;
                },

                // 处理Schema响应（已禁用）
                processSchemaResponse(parentCategoryId, data) {
                    console.log('⚠️ Schema响应处理功能已禁用');
                    return;
                },

                // 拒绝请求（已禁用）
                rejectRequest(categoryId, error) {
                    console.log('⚠️ 请求拒绝功能已禁用');
                    return;
                },

                // 清理过期请求（已禁用）
                cleanupExpiredRequests() {
                    console.log('⚠️ 过期请求清理功能已禁用');
                    return;
                },

                // 清理旧的响应缓存（已禁用）
                cleanupOldResponses() {
                    console.log('⚠️ 响应缓存清理功能已禁用');
                    return;
                }
            },

            // 获取上一级类目ID（已禁用）
            getParentCategoryId(categoryPath) {
                console.log('⚠️ 上一级类目ID获取功能已禁用');
                return null;

            },

            // 获取当前选中类目的ID（已禁用）
            getCurrentCategoryId() {
                console.log('⚠️ 当前类目ID获取功能已禁用');
                return null;
            },

            // 清理文件路径中的特殊字符
            cleanPathForFile(path) {
                return path.replace(/[\/\\:*?"<>|]/g, '_');
            },

            // 构建安全的文件路径
            buildSafeFilePath(categoryPath, fileName) {
                // 清理每个路径段中的特殊字符
                const cleanedPath = categoryPath.map(segment => this.cleanPathForFile(segment));
                const cleanedFileName = this.cleanPathForFile(fileName);

                // 构建完整路径
                if (cleanedPath.length > 1) {
                    const folderPath = cleanedPath.slice(0, -1).join('/');
                    return `categories/${folderPath}/${cleanedFileName}`;
                } else {
                    return `categories/${cleanedFileName}`;
                }
            },

            // 保存 schema 到本地文件（支持多级目录）
            saveSchemaToFile(schema, filePath) {
                const blob = new Blob([JSON.stringify(schema, null, 2)], {type: 'application/json'});
                const a = document.createElement('a');
                a.href = URL.createObjectURL(blob);
                a.download = filePath; // 支持多级目录
                document.body.appendChild(a);
                a.click();
                setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(a.href);
                }, 100);
            },

            // 新的商品必填项获取功能（启用网络监听和文件保存）
            async fetchAllRequiredFields() {
                console.log('🚀 启动商品必填项获取功能（带网络监听）');

                const result = {
                    success: false,
                    message: '',
                    processedCount: 0,
                    successCount: 0,
                    successRate: 0,
                    errors: [],
                    capturedResponses: [] // 新增：保存捕获的响应数据
                };

                try {
                    // 启动网络监听
                    this.setupNetworkListener();

                    // 获取所有类目
                    const categories = await this.getAllOpenedCategories();

                    if (!categories || categories.length === 0) {
                        result.errors.push('未找到任何类目');
                        result.message = '类目获取失败';
                        return result;
                    }

                    console.log(`📋 开始处理 ${categories.length} 个类目...`);
                    result.processedCount = categories.length;

                    // 逐个处理类目
                    for (let i = 0; i < categories.length; i++) {
                        const category = categories[i];
                        console.log(`🔄 处理类目 ${i + 1}/${categories.length}: ${category.name} (ID: ${category.id})`);

                        try {
                            // 点击类目并等待响应
                            const responseData = await this.clickCategoryAndCaptureResponse(category);

                            if (responseData) {
                                // 保存响应数据到文件
                                await this.saveResponseToFile(category, responseData);
                                result.successCount++;
                                result.capturedResponses.push({
                                    categoryId: category.id,
                                    categoryName: category.name,
                                    timestamp: new Date().toISOString(),
                                    dataSize: JSON.stringify(responseData).length
                                });
                                console.log(`✅ 类目 ${category.name} 数据保存成功`);
                            } else {
                                result.errors.push(`类目 ${category.name} 未获取到响应数据`);
                                console.log(`❌ 类目 ${category.name} 未获取到响应数据`);
                            }

                            // 等待一段时间避免请求过快
                            await this.delay(2000);

                        } catch (error) {
                            result.errors.push(`处理类目 ${category.name} 时出错: ${error.message}`);
                            console.error(`❌ 处理类目 ${category.name} 失败:`, error);
                        }
                    }

                    // 计算成功率
                    result.successRate = Math.round((result.successCount / result.processedCount) * 100);

                    if (result.successCount > 0) {
                        result.success = true;
                        result.message = `成功获取 ${result.successCount} 个类目的必填项数据`;

                        // 保存汇总信息
                        await this.saveSummaryFile(result);
                    } else {
                        result.message = '未成功获取任何类目数据';
                    }

                } catch (error) {
                    result.errors.push(`获取必填项功能执行失败: ${error.message}`);
                    result.message = '功能执行失败';
                    console.error('❌ fetchAllRequiredFields 执行失败:', error);
                } finally {
                    // 清理网络监听
                    this.clearNetworkListener();
                }

                console.log('📋 必填项获取结果:', result);
                return result;
            },

            // 网络监听相关方法（参考SpiderCrawl实现）
            networkListener: null,
            capturedData: new Map(), // 存储捕获的响应数据

            // 设置网络监听器
            setupNetworkListener() {
                console.log('🔧 设置网络监听器...');

                // 清理之前的监听器
                this.clearNetworkListener();

                // 保存原始的fetch方法
                if (!window.originalFetch) {
                    window.originalFetch = window.fetch;
                }

                // 拦截fetch请求
                window.fetch = async (...args) => {
                    const [url, options] = args;

                    try {
                        // 调用原始fetch
                        const response = await window.originalFetch(...args);

                        // 检查是否是getSchema请求
                        if (url && url.includes('/getSchema')) {
                            console.log('🎯 捕获到getSchema请求:', url);

                            // 克隆响应以便读取
                            const clonedResponse = response.clone();

                            try {
                                const responseData = await clonedResponse.json();
                                console.log('📦 获取到响应数据:', responseData);

                                // 提取类目ID
                                const categoryId = this.extractCategoryIdFromUrl(url) ||
                                                 this.extractCategoryIdFromRequest(options);

                                if (categoryId) {
                                    // 保存响应数据
                                    this.capturedData.set(categoryId, {
                                        url: url,
                                        request: options,
                                        response: responseData,
                                        timestamp: new Date().toISOString()
                                    });
                                    console.log(`💾 保存类目 ${categoryId} 的响应数据`);
                                }

                            } catch (parseError) {
                                console.error('❌ 解析响应数据失败:', parseError);
                            }
                        }

                        return response;

                    } catch (error) {
                        console.error('❌ 网络请求失败:', error);
                        throw error;
                    }
                };

                console.log('✅ 网络监听器设置完成');
            },

            // 清理网络监听器
            clearNetworkListener() {
                if (window.originalFetch) {
                    window.fetch = window.originalFetch;
                    console.log('🧹 网络监听器已清理');
                }
            },

            // 从URL中提取类目ID
            extractCategoryIdFromUrl(url) {
                try {
                    const urlObj = new URL(url);
                    const params = new URLSearchParams(urlObj.search);
                    return params.get('category_id') || null;
                } catch (error) {
                    console.error('❌ 提取类目ID失败:', error);
                    return null;
                }
            },

            // 从请求体中提取类目ID
            extractCategoryIdFromRequest(options) {
                try {
                    if (options && options.body) {
                        const body = typeof options.body === 'string' ?
                                   JSON.parse(options.body) : options.body;
                        return body?.context?.category_id || null;
                    }
                } catch (error) {
                    console.error('❌ 从请求体提取类目ID失败:', error);
                }
                return null;
            },

            // 点击类目并捕获响应
            async clickCategoryAndCaptureResponse(category) {
                console.log(`🖱️ 点击类目: ${category.name} (ID: ${category.id})`);

                try {
                    // 清除之前的数据
                    this.capturedData.delete(category.id);

                    // 点击类目
                    await this.clickCategory(category);

                    // 等待响应数据
                    const maxWaitTime = 10000; // 10秒超时
                    const checkInterval = 500; // 每500ms检查一次
                    let waitTime = 0;

                    while (waitTime < maxWaitTime) {
                        if (this.capturedData.has(category.id)) {
                            const data = this.capturedData.get(category.id);
                            console.log(`✅ 获取到类目 ${category.id} 的响应数据`);
                            return data;
                        }

                        await this.delay(checkInterval);
                        waitTime += checkInterval;
                    }

                    console.log(`⏰ 等待类目 ${category.id} 响应超时`);
                    return null;

                } catch (error) {
                    console.error(`❌ 点击类目 ${category.name} 失败:`, error);
                    return null;
                }
            },

            // 保存响应数据到文件
            async saveResponseToFile(category, responseData) {
                try {
                    const fileName = `categories/${category.name}_${category.id}_必填项.json`;
                    const fileContent = {
                        categoryInfo: {
                            id: category.id,
                            name: category.name,
                            level: category.level,
                            path: category.path
                        },
                        captureTime: new Date().toISOString(),
                        requestUrl: responseData.url,
                        responseData: responseData.response
                    };

                    // 使用现有的下载文件方法
                    this.downloadFile(fileName, JSON.stringify(fileContent, null, 2));
                    console.log(`💾 文件已保存: ${fileName}`);

                } catch (error) {
                    console.error('❌ 保存文件失败:', error);
                    throw error;
                }
            },

            // 保存汇总信息文件
            async saveSummaryFile(result) {
                try {
                    const fileName = `categories/必填项获取汇总_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    const summaryContent = {
                        summary: {
                            totalCategories: result.processedCount,
                            successCount: result.successCount,
                            successRate: result.successRate,
                            errors: result.errors
                        },
                        capturedResponses: result.capturedResponses,
                        timestamp: new Date().toISOString()
                    };

                    this.downloadFile(fileName, JSON.stringify(summaryContent, null, 2));
                    console.log(`📊 汇总文件已保存: ${fileName}`);

                } catch (error) {
                    console.error('❌ 保存汇总文件失败:', error);
                }
            },

            // 点击类目方法
            async clickCategory(category) {
                try {
                    // 根据类目层级选择不同的点击策略
                    if (category.level === 1) {
                        await this.clickFirstLevelCategory(category);
                    } else if (category.level === 2) {
                        await this.clickSecondLevelCategory(category);
                    } else if (category.level === 3) {
                        await this.clickThirdLevelCategory(category);
                    } else if (category.level === 4) {
                        await this.clickFourthLevelCategory(category);
                    }

                    // 等待页面响应
                    await this.delay(this.delayConfig.categorySwitch);

                } catch (error) {
                    console.error(`❌ 点击类目失败:`, error);
                    throw error;
                }
            },

            // 点击一级类目
            async clickFirstLevelCategory(category) {
                const selector = `.style_name__F9zo3 span:contains("${category.name}")`;
                const element = document.querySelector(selector);

                if (element) {
                    element.click();
                    await this.delay(this.delayConfig.level1Click);
                    console.log(`✅ 点击一级类目: ${category.name}`);
                } else {
                    throw new Error(`未找到一级类目元素: ${category.name}`);
                }
            },

            // 点击二级类目
            async clickSecondLevelCategory(category) {
                // 先确保一级类目已展开
                if (category.parentPath) {
                    await this.ensureParentCategoryExpanded(category.parentPath);
                }

                const selector = `.style_name__F9zo3 span:contains("${category.name}")`;
                const element = document.querySelector(selector);

                if (element) {
                    element.click();
                    await this.delay(this.delayConfig.level2Click);
                    console.log(`✅ 点击二级类目: ${category.name}`);
                } else {
                    throw new Error(`未找到二级类目元素: ${category.name}`);
                }
            },

            // 点击三级类目
            async clickThirdLevelCategory(category) {
                // 先确保父级类目已展开
                if (category.parentPath) {
                    await this.ensureParentCategoryExpanded(category.parentPath);
                }

                const selector = `.style_name__F9zo3 span:contains("${category.name}")`;
                const element = document.querySelector(selector);

                if (element) {
                    element.click();
                    await this.delay(this.delayConfig.level3Click);
                    console.log(`✅ 点击三级类目: ${category.name}`);
                } else {
                    throw new Error(`未找到三级类目元素: ${category.name}`);
                }
            },

            // 点击四级类目
            async clickFourthLevelCategory(category) {
                // 先确保父级类目已展开
                if (category.parentPath) {
                    await this.ensureParentCategoryExpanded(category.parentPath);
                }

                const selector = `.style_name__F9zo3 span:contains("${category.name}")`;
                const element = document.querySelector(selector);

                if (element) {
                    element.click();
                    await this.delay(this.delayConfig.level4Click);
                    console.log(`✅ 点击四级类目: ${category.name}`);
                } else {
                    throw new Error(`未找到四级类目元素: ${category.name}`);
                }
            },

            // 确保父级类目已展开
            async ensureParentCategoryExpanded(parentPath) {
                // 这里可以根据需要实现父级类目的展开逻辑
                console.log(`🔄 确保父级类目已展开: ${parentPath}`);
                // 简单实现：等待一段时间
                await this.delay(1000);
            },

            // 延迟时间配置（增加等待时间确保页面充分响应）
            delayConfig: {
                level1Click: 2500,      // 一级类目点击后等待时间（增加到2.5秒）
                level2Click: 2000,      // 二级类目点击后等待时间（增加到2秒）
                level3Click: 1800,      // 三级类目点击后等待时间（增加到1.8秒）
                level4Click: 1500,      // 四级类目点击后等待时间（增加到1.5秒）
                categorySwitch: 4000,   // 类目切换完成后等待时间（增加到4秒）
                betweenCategories: 4000, // 处理不同类目间的等待时间（增加到4秒）
                schemaTimeout: 20000,   // Schema响应超时时间（增加到20秒）
                stabilization: 1000     // 响应后稳定等待时间（增加到1秒）
            },

            // 工具方法：延迟执行
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },

            // 智能延迟方法（根据配置自动选择延迟时间）
            smartDelay(type) {
                const delayTime = this.delayConfig[type] || 1000;
                console.log(`⏳ 智能延迟 ${type}: ${delayTime}ms`);
                return this.delay(delayTime);
            }
        };

        // 暴露到全局
        window.douyinProductCreator = api;
        window.DouyinProductAPI = api;
        
        // 触发就绪事件
        window.dispatchEvent(new CustomEvent('douyinApiReady', { 
            detail: { api: api, integrated: true } 
        }));
        
        console.log('✅ API对象设置完成');
    }
    
    // 设置事件监听
    setupEventListeners() {
        // 监听来自content script的消息
        window.addEventListener('message', async (event) => {
            if (event.data.type === 'CREATE_PRODUCT_OPTIMIZED') {
                try {
                    const result = await window.douyinProductCreator.createProduct(event.data.product);
                    window.postMessage({
                        type: 'CREATE_PRODUCT_RESPONSE',
                        requestId: event.data.requestId,
                        success: true,
                        result: result
                    }, '*');
                } catch (error) {
                    window.postMessage({
                        type: 'CREATE_PRODUCT_RESPONSE',
                        requestId: event.data.requestId,
                        success: false,
                        error: error.message
                    }, '*');
                }
            }
        });

        // 监听来自background的Chrome消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            console.log('📨 Content Script 收到消息:', request);

            if (request.action === 'getAuthParams') {
                this.handleGetAuthParamsMessage(request, sendResponse);
                return true; // 保持消息通道开放
            }
        });

        console.log('✅ 事件监听设置完成');
    }

    // 处理获取认证参数的消息
    handleGetAuthParamsMessage(request, sendResponse) {
        try {
            console.log('🔑 开始获取页面认证参数...');

            // 尝试从页面中提取认证参数
            const authParams = this.extractAuthParams();

            if (authParams) {
                console.log('✅ 成功获取认证参数:', authParams);
                sendResponse({
                    success: true,
                    params: authParams
                });
            } else {
                console.log('⚠️ 未能获取到认证参数');
                sendResponse({
                    success: false,
                    error: '未能获取到认证参数'
                });
            }
        } catch (error) {
            console.error('❌ 获取认证参数失败:', error);
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    // 从页面中提取认证参数
    extractAuthParams() {
        try {
            let authParams = {};

            console.log('🔍 开始自动提取认证参数...');

            // 方法1: 从Cookie中提取参数（主要方法）
            const cookies = this.parseCookies();
            console.log('🍪 从Cookie中提取的参数:', {
                msToken: cookies.msToken ? '已获取' : '未获取',
                verifyFp: cookies.verifyFp ? '已获取' : '未获取',
                fp: cookies.fp ? '已获取' : '未获取',
                s_v_web_id: cookies.s_v_web_id ? '已获取' : '未获取'
            });

            if (cookies.msToken) authParams.msToken = cookies.msToken;
            if (cookies.verifyFp) authParams.verifyFp = cookies.verifyFp;
            if (cookies.fp) authParams.fp = cookies.fp;

            // 尝试从s_v_web_id获取fp参数（备用）
            if (!authParams.fp && cookies.s_v_web_id) {
                authParams.fp = cookies.s_v_web_id;
                console.log('🔄 使用s_v_web_id作为fp参数');
            }

            // 方法2: 从URL参数中提取（备用）
            const urlParams = new URLSearchParams(window.location.search);
            if (!authParams.msToken && urlParams.get('msToken')) authParams.msToken = urlParams.get('msToken');
            if (!authParams.verifyFp && urlParams.get('verifyFp')) authParams.verifyFp = urlParams.get('verifyFp');
            if (!authParams.fp && urlParams.get('fp')) authParams.fp = urlParams.get('fp');

            // 方法3: 从页面脚本中提取（备用）
            if (!authParams.msToken && window.msToken) authParams.msToken = window.msToken;
            if (!authParams.verifyFp && window.verifyFp) authParams.verifyFp = window.verifyFp;
            if (!authParams.fp && window.fp) authParams.fp = window.fp;

            // 生成动态参数
            authParams.lid = Date.now().toString();

            // 动态生成a_bogus参数
            if (typeof window.generateABogus === 'function') {
                try {
                    // 构建URL参数字符串用于a_bogus生成
                    const urlSearchParams = this.buildUrlSearchParams(authParams);
                    const userAgent = navigator.userAgent;

                    console.log('🔧 生成a_bogus参数...');

                    authParams.a_bogus = window.generateABogus(urlSearchParams, userAgent);
                    console.log('✅ a_bogus生成成功');
                } catch (error) {
                    console.error('❌ a_bogus生成失败:', error);
                    // 如果生成失败，尝试使用用户手动设置的参数
                    const storedParams = this.getStoredAuthParams();
                    if (storedParams && storedParams.a_bogus) {
                        authParams.a_bogus = storedParams.a_bogus;
                        console.log('🔄 使用已保存的a_bogus参数');
                    }
                }
            } else {
                console.warn('⚠️ a_bogus生成器未加载，尝试使用已保存的参数');
                const storedParams = this.getStoredAuthParams();
                if (storedParams && storedParams.a_bogus) {
                    authParams.a_bogus = storedParams.a_bogus;
                }
            }

            // 添加常用的请求头
            authParams.headers = {
                'Referer': window.location.href,
                'Origin': window.location.origin,
                'User-Agent': navigator.userAgent
            };

            console.log('🔍 最终使用的认证参数状态:', {
                msToken: authParams.msToken ? '✅ 已设置' : '❌ 未设置',
                a_bogus: authParams.a_bogus ? '✅ 已生成' : '❌ 未设置',
                verifyFp: authParams.verifyFp ? '✅ 已设置' : '❌ 未设置',
                fp: authParams.fp ? '✅ 已设置' : '❌ 未设置',
                lid: authParams.lid
            });

            // 检查是否有足够的认证参数
            const hasRequiredParams = authParams.msToken && authParams.verifyFp && authParams.fp;

            if (!hasRequiredParams) {
                console.warn('⚠️ 缺少必要的认证参数');
                console.warn('缺少的参数:', {
                    msToken: !authParams.msToken,
                    verifyFp: !authParams.verifyFp,
                    fp: !authParams.fp
                });

                // 尝试使用用户手动设置的参数作为备用
                const storedParams = this.getStoredAuthParams();
                if (storedParams) {
                    console.log('🔄 尝试使用手动设置的认证参数作为备用');
                    if (!authParams.msToken && storedParams.msToken) authParams.msToken = storedParams.msToken;
                    if (!authParams.a_bogus && storedParams.a_bogus) authParams.a_bogus = storedParams.a_bogus;
                    if (!authParams.verifyFp && storedParams.verifyFp) authParams.verifyFp = storedParams.verifyFp;
                    if (!authParams.fp && storedParams.fp) authParams.fp = storedParams.fp;

                    // 重新检查
                    const hasRequiredParamsAfterFallback = authParams.msToken && authParams.verifyFp && authParams.fp;
                    if (!hasRequiredParamsAfterFallback) {
                        console.error('🚫 即使使用备用参数仍然缺少必要的认证参数');
                        return null;
                    }
                    console.log('✅ 使用备用参数补全了认证参数');
                } else {
                    console.error('🚫 没有备用参数可用');
                    return null;
                }
            }

            // 如果没有a_bogus但有其他参数，尝试重新生成
            if (!authParams.a_bogus && authParams.msToken && typeof window.generateABogus === 'function') {
                try {
                    console.log('🔄 重新尝试生成a_bogus参数...');
                    const urlSearchParams = this.buildUrlSearchParams(authParams);
                    authParams.a_bogus = window.generateABogus(urlSearchParams, navigator.userAgent);
                    console.log('✅ a_bogus重新生成成功');
                } catch (error) {
                    console.error('❌ a_bogus重新生成失败:', error);
                }
            }

            return authParams;

        } catch (error) {
            console.error('💥 提取认证参数失败:', error);
            return null;
        }
    }

    // 解析Cookie获取认证参数
    parseCookies() {
        const cookies = {};
        try {
            document.cookie.split(';').forEach(cookie => {
                const [name, ...valueParts] = cookie.trim().split('=');
                if (name && valueParts.length > 0) {
                    const value = valueParts.join('='); // 处理值中包含=的情况
                    try {
                        cookies[name] = decodeURIComponent(value);
                    } catch (e) {
                        // 如果解码失败，使用原始值
                        cookies[name] = value;
                    }
                }
            });

            // 提取需要的认证参数
            const authCookies = {};

            // 主要认证参数
            if (cookies.msToken) authCookies.msToken = cookies.msToken;
            if (cookies.verifyFp) authCookies.verifyFp = cookies.verifyFp;
            if (cookies.fp) authCookies.fp = cookies.fp;

            // 备用参数
            if (cookies.s_v_web_id) authCookies.s_v_web_id = cookies.s_v_web_id;
            if (cookies.webid) authCookies.webid = cookies.webid;
            if (cookies.ttwid) authCookies.ttwid = cookies.ttwid;

            // 会话相关参数
            if (cookies.sessionid) authCookies.sessionid = cookies.sessionid;
            if (cookies.sid_tt) authCookies.sid_tt = cookies.sid_tt;

            console.log('🍪 解析到的Cookie参数:', Object.keys(authCookies));
            return authCookies;
        } catch (error) {
            console.error('💥 解析Cookie失败:', error);
            return {};
        }
    }

    // 构建URL参数字符串用于a_bogus生成
    buildUrlSearchParams(authParams) {
        const params = new URLSearchParams();

        // 基础参数
        params.set('device_platform', 'webapp');
        params.set('aid', '6383');
        params.set('channel', 'channel_pc_web');
        params.set('update_version_code', '170400');
        params.set('pc_client_type', '1');
        params.set('version_code', '170400');
        params.set('version_name', '17.4.0');
        params.set('cookie_enabled', 'true');

        // 屏幕和浏览器信息
        params.set('screen_width', screen.width.toString());
        params.set('screen_height', screen.height.toString());
        params.set('browser_language', navigator.language || 'zh-CN');
        params.set('browser_platform', navigator.platform || 'Win32');
        params.set('browser_name', 'Chrome');
        params.set('browser_version', this.getBrowserVersion());
        params.set('browser_online', 'true');

        // 引擎信息
        params.set('engine_name', 'Blink');
        params.set('engine_version', this.getBrowserVersion());

        // 操作系统信息
        params.set('os_name', this.getOSName());
        params.set('os_version', '10');

        // 硬件信息
        params.set('cpu_core_num', navigator.hardwareConcurrency || '8');
        params.set('device_memory', navigator.deviceMemory || '8');
        params.set('platform', 'PC');

        // 网络信息
        if (navigator.connection) {
            params.set('downlink', navigator.connection.downlink || '10');
            params.set('effective_type', navigator.connection.effectiveType || '4g');
            params.set('round_trip_time', navigator.connection.rtt || '50');
        } else {
            params.set('downlink', '10');
            params.set('effective_type', '4g');
            params.set('round_trip_time', '50');
        }

        // 认证参数
        if (authParams.webid) params.set('webid', authParams.webid);
        if (authParams.msToken) params.set('msToken', authParams.msToken);

        return params.toString();
    }

    // 获取浏览器版本
    getBrowserVersion() {
        const userAgent = navigator.userAgent;
        const match = userAgent.match(/Chrome\/([0-9.]+)/);
        return match ? match[1] : '*********';
    }

    // 获取操作系统名称
    getOSName() {
        const platform = navigator.platform.toLowerCase();
        if (platform.includes('win')) return 'Windows';
        if (platform.includes('mac')) return 'macOS';
        if (platform.includes('linux')) return 'Linux';
        return 'Windows';
    }

    // 日志输出
    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logArea = document.getElementById('log-area');
        const logEntry = document.createElement('div');
        
        const colors = {
            info: '#333',
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#faad14'
        };
        
        logEntry.style.color = colors[type] || colors.info;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logArea.appendChild(logEntry);
        logArea.scrollTop = logArea.scrollHeight;
        
        console.log(message);
    }
    
    // 更新状态
    updateStatus(message, type = 'info') {
        const statusDiv = document.getElementById('product-status');
        statusDiv.style.display = 'block';
        statusDiv.textContent = message;
        
        const colors = {
            info: '#f6f6f6',
            success: '#f6ffed',
            error: '#fff2f0',
            warning: '#fffbe6'
        };
        
        statusDiv.style.background = colors[type] || colors.info;
        
        this.log(message, type);
    }
    
    // 更新进度
    updateProgress(current, total) {
        const progressInfo = document.getElementById('progress-info');
        const progressText = document.getElementById('progress-text');
        const progressBar = document.getElementById('progress-bar');
        
        progressInfo.style.display = 'block';
        progressText.textContent = `${current}/${total}`;
        
        const percentage = total > 0 ? (current / total) * 100 : 0;
        progressBar.style.width = `${percentage}%`;
    }
    
    // 自动加载所有商品数据
    async loadProductData() {
        try {
            this.updateStatus('正在自动加载suppliers目录中的所有商品...', 'info');

            // 使用Chrome扩展API获取所有商品数据
            const products = await this.getAllProductsFromSuppliers();

            if (!products || products.length === 0) {
                this.updateStatus('未找到任何商品数据，请检查suppliers目录', 'warning');
                return;
            }

            this.products = products;
            this.updateStatus(`成功加载 ${products.length} 个商品`, 'success');

            // 显示商品列表
            this.displayProductList(products);

            // 启用批量创建按钮
            document.getElementById('start-batch-create').disabled = false;

        } catch (error) {
            this.updateStatus(`自动加载失败: ${error.message}`, 'error');
            this.log(`详细错误: ${error.stack}`, 'error');
        }
    }

    // 从suppliers目录获取所有商品数据 - 基于_detail_raw文件
    async getAllProductsFromSuppliers() {
        try {
            this.log('正在加载采集的商品数据...', 'info');

            // 方案1: 尝试通过background script从suppliers目录加载_detail_raw文件
            this.log('从suppliers目录加载_detail_raw文件...', 'info');

            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage(
                        { action: 'loadProducts' },
                        (response) => {
                            if (chrome.runtime.lastError) {
                                reject(new Error(chrome.runtime.lastError.message));
                            } else {
                                resolve(response);
                            }
                        }
                    );
                });

                if (response.success && response.products) {
                    this.log(`成功从suppliers目录加载了 ${response.products.length} 个商品`, 'success');

                    // 转换_detail_raw格式为标准格式
                    const standardProducts = this.convertDetailRawToStandard(response.products);
                    this.log(`转换后有效商品: ${standardProducts.length} 个`, 'success');

                    return standardProducts;
                }
            } catch (bgError) {
                this.log(`从suppliers目录加载失败: ${bgError.message}`, 'warning');
            }

            // 不使用预加载的测试数据，只使用真实采集的数据
            this.log('未找到采集的商品数据，请检查suppliers目录', 'error');
            this.log('请确保已运行商品采集脚本，并且suppliers目录中有_detail_raw.json文件', 'error');
            throw new Error('未找到采集的商品数据，请先运行商品采集脚本生成_detail_raw.json文件');

        } catch (error) {
            this.log(`获取商品数据失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 转换_detail_raw格式为标准格式
    convertDetailRawToStandard(detailRawProducts) {
        const standardProducts = [];

        detailRawProducts.forEach((rawProduct, index) => {
            try {
                // 从_detail_raw格式提取数据
                const productInfo = rawProduct.product_info || {};
                const supplierInfo = rawProduct.supplier_info || {};
                const detailData = rawProduct.detail_raw_data || {};

                // 构建标准格式
                const standardProduct = {
                    id: productInfo.id || detailData.spuId || `product_${index}`,
                    spu_id: productInfo.spu_id || detailData.spuId || productInfo.id,
                    name: productInfo.name || detailData.itemName || '未知商品',
                    images: detailData.itemMainImage || [],
                    price: detailData.minSalesPrice || detailData.salePrice || 0,
                    supply_price: detailData.minSupplyPrice || detailData.supplyPrice || 0,
                    sales_volume: detailData.salesVolume || 0,
                    brand: detailData.brandName || '无品牌',
                    category: '纸品湿巾', // 默认分类
                    supplier_id: supplierInfo.supplierId || '',
                    supplier_name: supplierInfo.coName || productInfo.supplier_name || '未知供应商',
                    source: '供应商模式',
                    collected_at: rawProduct.saved_at || new Date().toISOString(),
                    style_code: detailData.styleCode || '',
                    material_explain: detailData.materialExplain || '',
                    supplier_info: supplierInfo
                };

                // 验证必要字段
                if (standardProduct.name && standardProduct.images.length > 0) {
                    standardProducts.push(standardProduct);
                    console.log(`✅ 转换商品: ${standardProduct.name} (${standardProduct.images.length}张图片)`);
                } else {
                    console.warn(`⚠️ 跳过无效商品: ${standardProduct.name || '无名称'}`);
                }

            } catch (error) {
                console.error(`❌ 转换商品数据失败:`, error, rawProduct);
            }
        });

        return standardProducts;
    }

    // 显示商品列表
    displayProductList(products) {
        this.log('商品列表:', 'info');
        products.forEach((product, index) => {
            this.log(`${index + 1}. ${product.name} - ¥${product.price}`, 'info');
        });
    }

    // 获取商品必填项信息
    async fetchRequiredFields() {
        this.log('🚀 开始获取商品必填项信息...', 'info');

        // 初始化进度显示
        this.updateProgress(0, 25);

        // 禁用按钮防止重复点击
        const fetchBtn = document.getElementById('fetch-required-fields');
        const originalText = fetchBtn.textContent;
        fetchBtn.disabled = true;
        fetchBtn.textContent = '获取中...';

        try {
            // 监听后台处理进度
            const progressListener = (message) => {
                if (message.action === 'fetchProgress') {
                    const { current, total, categoryName, status } = message.data;

                    if (status === 'processing') {
                        this.updateProgress(current, total);
                        this.log(`📋 [${current}/${total}] 正在获取: ${categoryName}`, 'info');
                    } else if (status === 'success') {
                        this.log(`✅ [${current}/${total}] 成功: ${categoryName}`, 'success');
                    } else if (status === 'error') {
                        this.log(`❌ [${current}/${total}] 失败: ${categoryName}`, 'error');
                    }
                }
            };

            // 添加消息监听器
            chrome.runtime.onMessage.addListener(progressListener);

            // 发送消息到background script
            const response = await chrome.runtime.sendMessage({
                action: 'fetchRequiredFields'
            });

            // 移除监听器
            chrome.runtime.onMessage.removeListener(progressListener);

            if (response.success) {
                this.updateProgress(response.processedCount, response.processedCount);
                this.log(`🎉 获取完成！处理了 ${response.processedCount} 个类目`, 'success');
                this.log(`💾 成功保存 ${response.savedCount} 个文件 (成功率: ${response.successRate}%)`, 'success');

                if (response.retryCount > 0) {
                    this.log(`🔄 总共重试了 ${response.retryCount} 次`, 'info');
                }

                if (response.errors && response.errors.length > 0) {
                    this.log(`⚠️ 有 ${response.errors.length} 个类目处理失败`, 'warning');
                    response.errors.forEach(error => {
                        this.log(`   - ${error}`, 'error');
                    });

                    // 提供故障排除建议
                    this.log('💡 故障排除建议:', 'info');
                    this.log('   1. 确保已登录抖音小店', 'info');
                    this.log('   2. 检查网络连接是否正常', 'info');
                    this.log('   3. 尝试刷新页面重新获取认证参数', 'info');
                    this.log('   4. 可以点击"设置认证参数"手动配置', 'info');
                }

                // 显示下载提示
                this.log('📁 文件已自动下载到categories目录', 'info');
                this.log('💡 也可以在Chrome存储中查看数据', 'info');
                this.log('🔍 可以在浏览器开发者工具 > Application > Storage > Local Storage 中查看详细数据', 'info');

            } else {
                this.updateProgress(0, response.totalCategories || 25);
                this.log('❌ 获取必填项失败: ' + response.error, 'error');

                // 提供详细的错误处理建议
                if (response.error.includes('认证参数')) {
                    this.log('🔐 认证参数问题解决方案:', 'warning');
                    this.log('   1. 确保已登录抖音小店', 'info');
                    this.log('   2. 刷新页面重新获取Cookie', 'info');
                    this.log('   3. 点击"设置认证参数"手动配置', 'info');
                } else if (response.error.includes('网络')) {
                    this.log('🌐 网络问题解决方案:', 'warning');
                    this.log('   1. 检查网络连接', 'info');
                    this.log('   2. 确保能访问bscm.jinritemai.com', 'info');
                    this.log('   3. 稍后重试', 'info');
                }
            }
        } catch (error) {
            console.error('获取必填项失败:', error);
            this.updateProgress(0, 25);
            this.log('❌ 获取必填项失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            fetchBtn.disabled = false;
            fetchBtn.textContent = originalText;
        }
    }

    // 设置认证参数
    setupAuthParams() {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        `;

        // 先尝试自动获取当前参数
        const currentParams = this.extractAuthParams();
        const autoDetected = currentParams ? {
            msToken: currentParams.msToken ? '✅ 已自动获取' : '❌ 未检测到',
            verifyFp: currentParams.verifyFp ? '✅ 已自动获取' : '❌ 未检测到',
            fp: currentParams.fp ? '✅ 已自动获取' : '❌ 未检测到',
            a_bogus: currentParams.a_bogus ? '✅ 已自动生成' : '❌ 生成失败'
        } : {
            msToken: '❌ 未检测到',
            verifyFp: '❌ 未检测到',
            fp: '❌ 未检测到',
            a_bogus: '❌ 生成失败'
        };

        dialog.innerHTML = `
            <h3 style="margin-top: 0;">🔑 认证参数状态</h3>

            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                <h4 style="margin: 0 0 8px 0; color: #333;">自动检测结果:</h4>
                <div style="font-size: 14px; line-height: 1.6;">
                    <div>msToken (Cookie): ${autoDetected.msToken}</div>
                    <div>verifyFp (Cookie): ${autoDetected.verifyFp}</div>
                    <div>fp (Cookie): ${autoDetected.fp}</div>
                    <div>a_bogus (动态生成): ${autoDetected.a_bogus}</div>
                </div>
            </div>

            <div style="margin-bottom: 16px;">
                <p style="color: #666; font-size: 14px; margin: 0;">
                    <strong>说明:</strong><br>
                    • 大部分参数现在可以从Cookie自动获取<br>
                    • a_bogus参数会动态生成，无需手动设置<br>
                    • 如果自动获取失败，可以手动设置备用参数
                </p>
            </div>

            <details style="margin-bottom: 16px;">
                <summary style="cursor: pointer; color: #1890ff;">手动设置备用参数 (可选)</summary>
                <div style="margin-top: 12px;">
                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px;">msToken (备用):</label>
                        <input type="text" id="auth-msToken" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;"
                               placeholder="仅在自动获取失败时使用">
                    </div>

                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px;">verifyFp (备用):</label>
                        <input type="text" id="auth-verifyFp" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;"
                               placeholder="仅在自动获取失败时使用">
                    </div>

                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px;">fp (备用):</label>
                        <input type="text" id="auth-fp" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;"
                               placeholder="仅在自动获取失败时使用">
                    </div>

                    <div style="margin-bottom: 12px;">
                        <label style="display: block; margin-bottom: 4px;">a_bogus (备用):</label>
                        <input type="text" id="auth-a_bogus" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;"
                               placeholder="仅在动态生成失败时使用">
                    </div>
                </div>
            </details>

            <div style="margin-top: 20px; text-align: right;">
                <button id="auth-test" style="padding: 8px 16px; margin-right: 8px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    测试参数
                </button>
                <button id="auth-cancel" style="padding: 8px 16px; margin-right: 8px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">
                    关闭
                </button>
                <button id="auth-save" style="padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    保存备用参数
                </button>
            </div>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);

        // 加载已保存的参数
        const savedParams = this.getStoredAuthParams();
        if (savedParams) {
            if (savedParams.msToken) document.getElementById('auth-msToken').value = savedParams.msToken;
            if (savedParams.a_bogus) document.getElementById('auth-a_bogus').value = savedParams.a_bogus;
            if (savedParams.verifyFp) document.getElementById('auth-verifyFp').value = savedParams.verifyFp;
            if (savedParams.fp) document.getElementById('auth-fp').value = savedParams.fp;
        }

        // 事件处理
        document.getElementById('auth-cancel').onclick = () => {
            document.body.removeChild(modal);
        };

        document.getElementById('auth-test').onclick = async () => {
            this.log('🧪 开始测试认证参数...', 'info');

            // 1. 测试参数提取
            const testParams = this.extractAuthParams();
            if (!testParams) {
                this.log('❌ 无法提取认证参数', 'error');
                this.log('💡 请确保已登录抖音小店并刷新页面', 'warning');
                return;
            }

            // 2. 检查各个参数
            const paramStatus = {
                msToken: !!testParams.msToken,
                verifyFp: !!testParams.verifyFp,
                fp: !!testParams.fp,
                a_bogus: !!testParams.a_bogus
            };

            this.log('📋 参数检查结果:', 'info');
            Object.entries(paramStatus).forEach(([key, status]) => {
                this.log(`   ${key}: ${status ? '✅ 已获取' : '❌ 缺失'}`, status ? 'success' : 'error');
            });

            // 3. 如果基本参数都有，尝试发送测试请求
            if (paramStatus.msToken && paramStatus.verifyFp && paramStatus.fp) {
                this.log('🔗 发送测试请求...', 'info');
                try {
                    // 发送测试消息到background script
                    const testResult = await chrome.runtime.sendMessage({
                        action: 'testAuthParams',
                        params: testParams
                    });

                    if (testResult && testResult.success) {
                        this.log('✅ 认证参数测试通过！API请求成功', 'success');
                        this.log('🎉 您可以正常使用商品必填项功能', 'success');
                    } else {
                        this.log('❌ API请求失败: ' + (testResult?.error || '未知错误'), 'error');
                        this.log('💡 建议检查登录状态或重新获取认证参数', 'warning');
                    }
                } catch (error) {
                    this.log('❌ 测试请求发送失败: ' + error.message, 'error');
                }
            } else {
                this.log('❌ 基本认证参数不完整，无法进行API测试', 'error');
                this.log('💡 请检查登录状态或手动设置备用参数', 'warning');
            }
        };

        document.getElementById('auth-save').onclick = () => {
            const params = {
                msToken: document.getElementById('auth-msToken').value.trim(),
                a_bogus: document.getElementById('auth-a_bogus').value.trim(),
                verifyFp: document.getElementById('auth-verifyFp').value.trim(),
                fp: document.getElementById('auth-fp').value.trim()
            };

            // 只保存非空的备用参数
            const backupParams = {};
            if (params.msToken) backupParams.msToken = params.msToken;
            if (params.a_bogus) backupParams.a_bogus = params.a_bogus;
            if (params.verifyFp) backupParams.verifyFp = params.verifyFp;
            if (params.fp) backupParams.fp = params.fp;

            // 保存备用参数
            this.saveAuthParams(backupParams);

            if (Object.keys(backupParams).length > 0) {
                this.log(`✅ 已保存 ${Object.keys(backupParams).length} 个备用认证参数`, 'success');
            } else {
                this.log('ℹ️ 未设置任何备用参数，将完全依赖自动获取', 'info');
            }

            document.body.removeChild(modal);
        };

        // 点击背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    // 保存认证参数
    saveAuthParams(params) {
        try {
            localStorage.setItem('douyin_auth_params', JSON.stringify(params));
            console.log('认证参数已保存到localStorage');
        } catch (error) {
            console.error('保存认证参数失败:', error);
        }
    }

    // 获取已保存的认证参数
    getStoredAuthParams() {
        try {
            const stored = localStorage.getItem('douyin_auth_params');
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.error('读取认证参数失败:', error);
            return null;
        }
    }

    // 开始批量创建 - 一个个创建，完成一个再开始下一个
    async startBatchCreate() {
        if (this.products.length === 0) {
            this.updateStatus('请先加载商品数据', 'warning');
            return;
        }

        // 测试API连接
        const connectionTest = window.douyinProductCreator.testConnection();
        if (!connectionTest.ready) {
            this.updateStatus('API连接未就绪，请确保已登录抖音小店', 'error');
            return;
        }

        this.updateStatus('开始被动辅助创建商品...', 'info');
        this.log('ℹ️ 被动辅助模式：系统将辅助填写商品信息，不会自动点击页面按钮', 'info');
        this.log('💡 用户需要手动点击"下一步"等按钮来推进流程', 'info');

        // 初始化创建状态
        this.currentProductIndex = 0;
        this.creationResults = [];

        // 禁用批量创建按钮
        document.getElementById('start-batch-create').disabled = true;

        // 开始创建第一个商品
        await this.createNextProduct();
    }

    // 创建下一个商品
    async createNextProduct() {
        try {
            // 检查是否已经在创建商品
            if (window.douyinProductCreator && window.douyinProductCreator.isCreatingProduct) {
                console.warn('⚠️ 已有商品正在创建中，跳过重复请求');
                return;
            }

            if (this.currentProductIndex >= this.products.length) {
                // 所有商品创建完成
                await this.showFinalResults();
                return;
            }

            // 设置创建状态
            if (window.douyinProductCreator) {
                window.douyinProductCreator.isCreatingProduct = true;
                console.log('🔒 开始创建商品，设置创建状态锁');
            }

            // 清理之前商品的监听器，避免干扰
            this.clearWatchers();

            const product = this.products[this.currentProductIndex];
            const productNumber = this.currentProductIndex + 1;

            this.updateProgress(this.currentProductIndex, this.products.length);
            this.log(`\n=== 创建第 ${productNumber}/${this.products.length} 个商品 ===`, 'info');
            this.log(`商品名称: ${product.name}`, 'info');

            try {
                // 创建商品
                const result = await window.douyinProductCreator.createProduct(product);

                this.creationResults.push({
                    success: result.success,
                    product: product,
                    result: result,
                    index: this.currentProductIndex
                });

                if (result.success) {
                    this.log(`✅ 第 ${productNumber} 个商品创建成功`, 'success');
                } else {
                    this.log(`❌ 第 ${productNumber} 个商品创建失败`, 'error');
                }

                // 显示下一步操作提示
                this.showNextProductButton();

            } catch (error) {
                this.log(`❌ 第 ${productNumber} 个商品创建失败: ${error.message}`, 'error');

                this.creationResults.push({
                    success: false,
                    product: product,
                    error: error.message,
                    index: this.currentProductIndex
                });

                // 即使失败也继续下一个商品
                this.showNextProductButton();
            }

            // 释放创建状态锁
            if (window.douyinProductCreator) {
                window.douyinProductCreator.isCreatingProduct = false;
                console.log('🔓 商品创建完成，释放创建状态锁');
            }

        } catch (error) {
            this.log(`❌ 创建商品过程中发生错误: ${error.message}`, 'error');
            if (window.douyinProductCreator) {
                window.douyinProductCreator.isCreatingProduct = false;
                console.log('🔓 商品创建出错，释放创建状态锁');
            }
            this.showNextProductButton();
        }
    }

    // 显示下一个商品按钮
    showNextProductButton() {
        try {
            // 移除现有的下一个商品按钮
            const existingButton = document.getElementById('next-product-button');
            if (existingButton) {
                existingButton.remove();
            }

            // 创建下一个商品按钮
            const nextButton = document.createElement('button');
            nextButton.id = 'next-product-button';
            nextButton.textContent = this.currentProductIndex < this.products.length - 1 ?
                `继续创建下一个商品 (${this.currentProductIndex + 2}/${this.products.length})` :
                '完成所有商品创建';
            nextButton.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 10000;
                padding: 12px 24px;
                background: #1890ff;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
                animation: pulse 2s infinite;
            `;

            // 添加CSS动画
            if (!document.getElementById('next-button-style')) {
                const style = document.createElement('style');
                style.id = 'next-button-style';
                style.textContent = `
                    @keyframes pulse {
                        0% { box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3); }
                        50% { box-shadow: 0 4px 20px rgba(24, 144, 255, 0.6); }
                        100% { box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3); }
                    }
                `;
                document.head.appendChild(style);
            }

            // 添加点击事件
            nextButton.addEventListener('click', async () => {
                nextButton.disabled = true;
                nextButton.textContent = '正在处理...';

                // 移动到下一个商品
                this.currentProductIndex++;

                // 移除按钮
                nextButton.remove();

                // 创建下一个商品
                await this.createNextProduct();
            });

            // 添加到页面
            document.body.appendChild(nextButton);

            // 显示提示信息
            if (this.currentProductIndex < this.products.length - 1) {
                this.log(`⏳ 请检查当前商品信息，确认无误后点击"继续创建下一个商品"按钮`, 'info');
                this.updateStatus(`等待继续创建第 ${this.currentProductIndex + 2} 个商品...`, 'info');
            } else {
                this.log(`⏳ 这是最后一个商品，请检查信息后点击"完成所有商品创建"按钮`, 'info');
                this.updateStatus('等待完成最后一个商品...', 'info');
            }

        } catch (error) {
            console.error('显示下一个商品按钮失败:', error);
        }
    }

    // 显示最终结果
    async showFinalResults() {
        try {
            const successCount = this.creationResults.filter(r => r.success).length;
            const failCount = this.creationResults.length - successCount;

            this.updateProgress(this.products.length, this.products.length);
            this.updateStatus(`批量创建完成! 成功: ${successCount}, 失败: ${failCount}`, successCount > 0 ? 'success' : 'warning');

            this.log(`\n🎉 所有商品创建完成！`, 'success');
            this.log(`📊 创建统计:`, 'info');
            this.log(`  ✅ 成功: ${successCount} 个`, 'success');
            this.log(`  ❌ 失败: ${failCount} 个`, failCount > 0 ? 'error' : 'info');
            this.log(`  📦 总计: ${this.creationResults.length} 个`, 'info');

            // 显示详细结果
            if (failCount > 0) {
                this.log(`\n❌ 失败的商品:`, 'error');
                this.creationResults.filter(r => !r.success).forEach((result, index) => {
                    this.log(`  ${index + 1}. ${result.product.name}: ${result.error || '创建失败'}`, 'error');
                });
            }

            // 移除下一个商品按钮
            const nextButton = document.getElementById('next-product-button');
            if (nextButton) {
                nextButton.remove();
            }

            // 重新启用批量创建按钮
            document.getElementById('start-batch-create').disabled = false;

            // 重置状态
            this.currentProductIndex = 0;
            this.creationResults = [];

        } catch (error) {
            console.error('显示最终结果失败:', error);
        }
    }

    // 测试全局监听线程
    testGlobalListener() {
        console.log('🧪 测试全局监听线程...');

        // 启动监听线程
        window.douyinProductCreator.schemaListenerManager.start();

        // 模拟请求
        const testCategoryId = 'test_category_123';
        const testPath = ['测试', '类目', '路径'];

        console.log('📝 发送测试请求...');
        window.douyinProductCreator.schemaListenerManager.requestSchema(testCategoryId, testPath, 5000)
            .then(result => {
                console.log('✅ 测试请求结果:', result);
            })
            .catch(error => {
                console.error('❌ 测试请求失败:', error);
            });

        console.log('📊 当前监听队列状态:');
        console.log('- 待处理请求数:', window.douyinProductCreator.schemaListenerManager.pendingRequests.size);
        console.log('- 缓存响应数:', window.douyinProductCreator.schemaListenerManager.capturedResponses.size);
        console.log('- 监听线程状态:', window.douyinProductCreator.schemaListenerManager.isRunning ? '运行中' : '已停止');
    }

    // 查看监听线程状态
    checkListenerStatus() {
        console.log('📊 全局监听线程状态报告:');
        console.log('- 运行状态:', window.douyinProductCreator.schemaListenerManager.isRunning ? '✅ 运行中' : '❌ 已停止');
        console.log('- 待处理请求数:', window.douyinProductCreator.schemaListenerManager.pendingRequests.size);
        console.log('- 缓存响应数:', window.douyinProductCreator.schemaListenerManager.capturedResponses.size);

        if (window.douyinProductCreator.schemaListenerManager.pendingRequests.size > 0) {
            console.log('📋 待处理请求列表:');
            window.douyinProductCreator.schemaListenerManager.pendingRequests.forEach((request, categoryId) => {
                const elapsed = Date.now() - request.timestamp;
                console.log(`  - ${categoryId}: ${request.categoryPath.join(' > ')} (等待${Math.round(elapsed/1000)}秒)`);
            });
        }

        if (window.douyinProductCreator.schemaListenerManager.capturedResponses.size > 0) {
            console.log('📦 缓存响应列表:');
            window.douyinProductCreator.schemaListenerManager.capturedResponses.forEach((data, categoryId) => {
                console.log(`  - ${categoryId}: ${data.errno === 0 ? '成功' : '失败'}`);
            });
        }
    }

    // 测试上一级类目ID提取
    testParentCategoryId() {
        console.log('🧪 测试上一级类目ID提取...');

        // 获取当前选中的类目路径
        const currentPath = window.douyinProductCreator.getCurrentSelectedCategoryPath();
        if (!currentPath || currentPath.length === 0) {
            console.warn('⚠️ 未找到当前选中的类目路径');
            return;
        }

        console.log('📋 当前类目路径:', currentPath.join(' > '));

        // 测试获取上一级类目ID
        const parentId = window.douyinProductCreator.getParentCategoryId(currentPath);
        if (parentId) {
            console.log(`✅ 成功获取上一级类目ID: ${parentId}`);
            console.log(`📝 这个ID将用于API请求中的category_id参数`);
            console.log(`📝 对应的最终类目: ${currentPath[currentPath.length - 1]}`);
            console.log(`📝 对应的上一级类目: ${currentPath.length > 1 ? currentPath[currentPath.length - 2] : '无'}`);
        } else {
            console.error('❌ 无法获取上一级类目ID');
        }

        return {
            currentPath,
            parentId,
            finalCategory: currentPath[currentPath.length - 1],
            parentCategory: currentPath.length > 1 ? currentPath[currentPath.length - 2] : null
        };
    }
}

// 只在抖音小店创建商品页面才初始化扩展
function shouldInitializeExtension() {
    const url = window.location.href;
    console.log('🔍 检查页面URL:', url);

    const hasCorrectDomain = url.includes('bscm.jinritemai.com');
    const hasCreatePath = url.includes('cargo/create');

    console.log('🔍 域名检查:', hasCorrectDomain);
    console.log('🔍 路径检查:', hasCreatePath);

    return hasCorrectDomain && hasCreatePath;
}

// 强制初始化函数
function forceInitializeExtension() {
    console.log('🔧 强制初始化扩展...');
    try {
        if (!window.douyinExtension) {
            window.douyinExtension = new DouyinProductExtension();
            console.log('✅ 扩展强制初始化成功');
        } else {
            console.log('⚠️ 扩展实例已存在');
        }
    } catch (error) {
        console.error('❌ 扩展强制初始化失败:', error);
    }
}

// 等待页面加载完成后有条件地初始化
const shouldInit = shouldInitializeExtension();
console.log('🎯 是否应该初始化扩展:', shouldInit);

if (shouldInit) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 在抖音小店创建商品页面初始化扩展 (DOMContentLoaded)');
            try {
                window.douyinExtension = new DouyinProductExtension();
            } catch (error) {
                console.error('❌ 扩展初始化失败:', error);
            }
        });
    } else {
        console.log('🎯 在抖音小店创建商品页面初始化扩展 (立即)');
        try {
            window.douyinExtension = new DouyinProductExtension();
        } catch (error) {
            console.error('❌ 扩展初始化失败:', error);
        }
    }
} else {
    console.log('📦 抖音小店扩展已加载，但当前页面不是创建商品页面，跳过初始化');
    console.log('💡 如果您确实在商品创建页面，请在控制台运行: forceInitializeExtension()');
}

// 将强制初始化函数暴露到全局
window.forceInitializeExtension = forceInitializeExtension;

console.log('📦 抖音小店扩展整合版本加载完成');
