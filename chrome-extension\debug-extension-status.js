// 扩展状态调试脚本
console.log('🔍 开始检查扩展状态...');

// 检查页面URL
console.log('📍 当前页面URL:', window.location.href);

// 检查是否在正确的页面
const isCorrectPage = window.location.href.includes('bscm.jinritemai.com/views/micro/ffa-goods/cargo/create');
console.log('✅ 是否在商品创建页面:', isCorrectPage);

// 检查扩展面板是否存在
const panel = document.getElementById('douyin-extension-panel');
console.log('🎛️ 扩展面板是否存在:', !!panel);

// 检查API对象是否存在
console.log('🔧 API对象是否存在:', !!window.douyinProductCreator);

// 检查扩展实例是否存在
console.log('📦 扩展实例是否存在:', !!window.douyinExtension);

// 检查脚本是否加载
const scripts = Array.from(document.scripts).map(s => s.src).filter(src => src.includes('all-in-one.js'));
console.log('📜 all-in-one.js脚本是否加载:', scripts.length > 0);

// 检查控制台错误
console.log('⚠️ 请检查控制台是否有JavaScript错误');

// 如果面板不存在，尝试手动创建
if (!panel && isCorrectPage) {
    console.log('🔧 面板不存在，尝试手动初始化...');
    
    // 检查是否有扩展类
    if (typeof DouyinProductExtension !== 'undefined') {
        console.log('✅ 找到扩展类，尝试创建实例...');
        try {
            window.douyinExtension = new DouyinProductExtension();
            console.log('✅ 扩展实例创建成功');
        } catch (error) {
            console.error('❌ 扩展实例创建失败:', error);
        }
    } else {
        console.log('❌ 未找到扩展类 DouyinProductExtension');
    }
}

// 显示当前状态摘要
setTimeout(() => {
    console.log('\n📊 扩展状态摘要:');
    console.log('- 页面正确:', isCorrectPage);
    console.log('- 面板存在:', !!document.getElementById('douyin-extension-panel'));
    console.log('- API对象:', !!window.douyinProductCreator);
    console.log('- 扩展实例:', !!window.douyinExtension);
    
    if (!isCorrectPage) {
        console.log('💡 建议: 请确保在抖音小店商品创建页面使用扩展');
    }
    
    if (isCorrectPage && !document.getElementById('douyin-extension-panel')) {
        console.log('💡 建议: 扩展可能未正确加载，请刷新页面或重新安装扩展');
    }
}, 1000);
