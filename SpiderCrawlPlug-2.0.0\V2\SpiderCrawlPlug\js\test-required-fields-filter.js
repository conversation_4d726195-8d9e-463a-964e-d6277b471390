// 必填项过滤器测试脚本
// 用于测试和验证必填项过滤功能

class RequiredFieldsFilterTest {
    constructor() {
        this.testResults = [];
        this.testUrls = [
            // 抖音小店商品必填项Schema API - 应该匹配
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123',
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123&brand_id=456',

            // 相似但不完全匹配的URL - 可能匹配（备用模式）
            'https://bscm.jinritemai.com/other/getSchema',
            'https://bscm.jinritemai.com/api/tproduct/info',

            // 非匹配URL（应该被过滤掉）
            'https://example.com/api/user/profile',
            'https://bscm.jinritemai.com/api/user/login',
            'https://bscm.jinritemai.com/api/goods/list',
            'https://static.jinritemai.com/images/logo.png',
            'https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create'
        ];
    }
    
    // 运行所有测试
    runAllTests() {
        console.log('🧪 开始运行必填项过滤器测试...');
        
        this.testUrlMatching();
        this.testFilterConfiguration();
        this.testDataProcessing();
        this.testFileExport();
        
        this.printTestResults();
    }
    
    // 测试URL匹配功能
    testUrlMatching() {
        console.log('📋 测试URL匹配功能...');
        
        // 检查是否有过滤器实例
        if (typeof window.requiredFieldsFilter === 'undefined') {
            this.addTestResult('URL匹配测试', false, '过滤器实例不存在');
            return;
        }
        
        const filter = window.requiredFieldsFilter;
        let passedCount = 0;
        let totalCount = 0;
        
        this.testUrls.forEach(url => {
            totalCount++;
            const result = filter.isRequiredFieldsRequest(url);
            const shouldMatch = this.shouldUrlMatch(url);
            
            if (result.matched === shouldMatch) {
                passedCount++;
                console.log(`✅ ${url} - 匹配结果正确`);
            } else {
                console.log(`❌ ${url} - 匹配结果错误，期望: ${shouldMatch}, 实际: ${result.matched}`);
            }
        });
        
        const success = passedCount === totalCount;
        this.addTestResult('URL匹配测试', success, `${passedCount}/${totalCount} 个URL匹配正确`);
    }
    
    // 判断URL是否应该匹配
    shouldUrlMatch(url) {
        // 精确匹配的URL
        const exactMatchUrls = [
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123',
            'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema?category_id=123&brand_id=456'
        ];

        // 模式匹配的URL（备用）
        const patternMatchUrls = [
            'https://bscm.jinritemai.com/other/getSchema',
            'https://bscm.jinritemai.com/api/tproduct/info'
        ];

        // 不匹配的URL
        const nonMatchingUrls = [
            'https://example.com/api/user/profile',
            'https://bscm.jinritemai.com/api/user/login',
            'https://bscm.jinritemai.com/api/goods/list',
            'https://static.jinritemai.com/images/logo.png',
            'https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create'
        ];

        // 精确匹配优先
        if (exactMatchUrls.some(exactUrl => url === exactUrl || url.startsWith(exactUrl))) {
            return true;
        }

        // 模式匹配（可能匹配）
        if (patternMatchUrls.includes(url)) {
            return true;
        }

        // 明确不匹配的URL
        if (nonMatchingUrls.includes(url)) {
            return false;
        }

        // 默认不匹配
        return false;
    }
    
    // 测试过滤器配置
    testFilterConfiguration() {
        console.log('⚙️ 测试过滤器配置...');
        
        if (typeof window.requiredFieldsFilter === 'undefined') {
            this.addTestResult('配置测试', false, '过滤器实例不存在');
            return;
        }
        
        const filter = window.requiredFieldsFilter;
        
        // 测试启用/禁用
        const originalState = filter.isEnabled;
        
        filter.setEnabled(true);
        const enabledCorrectly = filter.isEnabled === true;
        
        filter.setEnabled(false);
        const disabledCorrectly = filter.isEnabled === false;
        
        // 恢复原始状态
        filter.setEnabled(originalState);
        
        const success = enabledCorrectly && disabledCorrectly;
        this.addTestResult('配置测试', success, success ? '启用/禁用功能正常' : '启用/禁用功能异常');
    }
    
    // 测试数据处理
    testDataProcessing() {
        console.log('📊 测试数据处理功能...');
        
        if (typeof window.requiredFieldsFilter === 'undefined') {
            this.addTestResult('数据处理测试', false, '过滤器实例不存在');
            return;
        }
        
        const filter = window.requiredFieldsFilter;
        
        // 创建测试数据
        const testRequestData = {
            url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
            method: 'GET',
            request: {
                url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
                method: 'GET',
                headers: [
                    { name: 'Content-Type', value: 'application/json' },
                    { name: 'User-Agent', value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' }
                ],
                queryString: [
                    { name: 'category_id', value: '123456' }
                ],
                postData: null
            },
            response: {
                status: 200,
                headers: [
                    { name: 'Content-Type', value: 'application/json' },
                    { name: 'Server', value: 'nginx' }
                ],
                content: '{"schema": {"required_fields": ["product_name", "price", "category_id", "brand_id"], "optional_fields": ["description", "images"]}}'
            }
        };
        
        const processedData = filter.processRequiredFieldsData(testRequestData);
        
        const success = processedData !== null && 
                       processedData.platform === 'douyin' &&
                       processedData.url === testRequestData.url;
        
        this.addTestResult('数据处理测试', success, success ? '数据处理正常' : '数据处理异常');
    }
    
    // 测试文件导出
    testFileExport() {
        console.log('💾 测试文件导出功能...');
        
        if (typeof window.requiredFieldsFilter === 'undefined') {
            this.addTestResult('文件导出测试', false, '过滤器实例不存在');
            return;
        }
        
        const filter = window.requiredFieldsFilter;
        
        // 创建测试数据
        const testData = {
            id: 1,
            timestamp: new Date().toISOString(),
            platform: 'douyin',
            matchType: 'exact',
            matchValue: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
            url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
            method: 'GET',
            status: 200,
            request: {
                url: 'https://bscm.jinritemai.com/fxg/product/tproduct/getSchema',
                method: 'GET',
                headers: [{ name: 'Content-Type', value: 'application/json' }],
                queryString: [{ name: 'category_id', value: '123456' }]
            },
            response: {
                status: 200,
                headers: [{ name: 'Content-Type', value: 'application/json' }],
                content: '{"schema": {"required_fields": ["product_name", "price"]}}'
            }
        };
        
        try {
            // 注意：这里不会真正下载文件，只是测试函数是否正常执行
            const result = filter.saveToFile(testData, 'test_export.json');
            const success = result.success === true;
            this.addTestResult('文件导出测试', success, success ? '导出功能正常' : '导出功能异常');
        } catch (error) {
            this.addTestResult('文件导出测试', false, `导出功能异常: ${error.message}`);
        }
    }
    
    // 添加测试结果
    addTestResult(testName, success, message) {
        this.testResults.push({
            name: testName,
            success: success,
            message: message,
            timestamp: new Date().toISOString()
        });
    }
    
    // 打印测试结果
    printTestResults() {
        console.log('\n📋 测试结果汇总:');
        console.log('='.repeat(50));
        
        let passedCount = 0;
        let totalCount = this.testResults.length;
        
        this.testResults.forEach(result => {
            const status = result.success ? '✅ 通过' : '❌ 失败';
            console.log(`${status} ${result.name}: ${result.message}`);
            if (result.success) passedCount++;
        });
        
        console.log('='.repeat(50));
        console.log(`总计: ${passedCount}/${totalCount} 个测试通过`);
        
        if (passedCount === totalCount) {
            console.log('🎉 所有测试通过！必填项过滤功能正常工作。');
        } else {
            console.log('⚠️ 部分测试失败，请检查相关功能。');
        }
        
        return {
            passed: passedCount,
            total: totalCount,
            success: passedCount === totalCount,
            results: this.testResults
        };
    }
    
    // 生成测试报告
    generateTestReport() {
        const report = {
            testTime: new Date().toISOString(),
            summary: {
                total: this.testResults.length,
                passed: this.testResults.filter(r => r.success).length,
                failed: this.testResults.filter(r => r.success === false).length
            },
            details: this.testResults,
            environment: {
                userAgent: navigator.userAgent,
                url: window.location.href,
                filterVersion: window.requiredFieldsFilter ? '1.0.0' : 'N/A'
            }
        };
        
        return report;
    }
    
    // 导出测试报告
    exportTestReport() {
        const report = this.generateTestReport();
        const jsonData = JSON.stringify(report, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `required_fields_filter_test_report_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('📄 测试报告已导出');
    }
}

// 创建全局测试实例
window.requiredFieldsFilterTest = new RequiredFieldsFilterTest();

// 提供便捷的测试函数
window.testRequiredFieldsFilter = function() {
    return window.requiredFieldsFilterTest.runAllTests();
};

window.exportFilterTestReport = function() {
    return window.requiredFieldsFilterTest.exportTestReport();
};

console.log('🧪 必填项过滤器测试脚本已加载');
console.log('💡 使用 testRequiredFieldsFilter() 运行测试');
console.log('💡 使用 exportFilterTestReport() 导出测试报告');
