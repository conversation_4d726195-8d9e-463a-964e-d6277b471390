# 抖音小店扩展故障排除指南

## 问题：扩展不能正常工作

### 快速检查步骤

1. **确认页面正确**
   - 确保您在抖音小店商品创建页面
   - URL应该包含：`bscm.jinritemai.com/views/micro/ffa-goods/cargo/create`

2. **检查扩展是否加载**
   - 按F12打开开发者工具
   - 在控制台(Console)中查看是否有扩展相关的日志
   - 应该看到类似 "🚀 抖音小店扩展整合版本启动..." 的消息

3. **查看扩展面板**
   - 在页面右上角应该有一个蓝色边框的扩展面板
   - 标题为 "🚀 抖音小店批量创建工具"

### 快速修复方法

#### 方法1：在控制台运行调试脚本
```javascript
// 复制以下代码到控制台并回车
const script = document.createElement('script');
script.src = chrome.runtime.getURL('debug-extension-status.js');
document.head.appendChild(script);
```

#### 方法2：手动强制初始化
```javascript
// 在控制台运行
if (typeof forceInitializeExtension === 'function') {
    forceInitializeExtension();
} else {
    console.log('强制初始化函数不可用，请刷新页面');
}
```

#### 方法3：运行快速修复
```javascript
// 在控制台运行
const script = document.createElement('script');
script.src = chrome.runtime.getURL('quick-fix.js');
document.head.appendChild(script);
```

### 常见问题及解决方案

#### 问题1：扩展面板不显示
**解决方案：**
1. 刷新页面
2. 确认扩展已启用
3. 在控制台运行快速修复脚本

#### 问题2：按钮无响应
**解决方案：**
1. 检查控制台是否有JavaScript错误
2. 重新加载扩展
3. 清除浏览器缓存

#### 问题3：页面URL不匹配
**解决方案：**
1. 确保在正确的商品创建页面
2. 如果URL正确但扩展仍不工作，手动强制初始化

### 重新安装扩展

如果以上方法都无效：

1. 打开Chrome扩展管理页面 (chrome://extensions/)
2. 找到"抖音小店商品批量创建助手"
3. 点击"重新加载"按钮
4. 或者删除扩展后重新安装

### 获取详细日志

在控制台运行以下代码获取详细状态信息：
```javascript
console.log('=== 扩展状态检查 ===');
console.log('页面URL:', window.location.href);
console.log('扩展面板:', !!document.getElementById('douyin-extension-panel'));
console.log('扩展实例:', !!window.douyinExtension);
console.log('API对象:', !!window.douyinProductCreator);
console.log('扩展类:', typeof DouyinProductExtension);
```

### 联系支持

如果问题仍然存在，请提供：
1. 浏览器版本
2. 扩展版本
3. 控制台错误信息
4. 页面URL
5. 问题描述
