// 快速修复脚本 - 手动检查和修复扩展问题
console.log('🔧 开始快速修复检查...');

// 1. 检查当前页面
const currentUrl = window.location.href;
console.log('📍 当前页面:', currentUrl);

const isCorrectPage = currentUrl.includes('bscm.jinritemai.com') && currentUrl.includes('cargo/create');
console.log('✅ 页面检查:', isCorrectPage ? '正确' : '错误');

if (!isCorrectPage) {
    console.log('❌ 当前不在商品创建页面，扩展无法工作');
    console.log('💡 请导航到: https://bscm.jinritemai.com/views/micro/ffa-goods/cargo/create');
    return;
}

// 2. 检查扩展面板
let panel = document.getElementById('douyin-extension-panel');
console.log('🎛️ 扩展面板存在:', !!panel);

// 3. 检查扩展实例
console.log('📦 扩展实例存在:', !!window.douyinExtension);

// 4. 检查API对象
console.log('🔧 API对象存在:', !!window.douyinProductCreator);

// 5. 如果面板不存在，尝试修复
if (!panel) {
    console.log('🔧 面板不存在，尝试修复...');
    
    // 检查扩展类是否可用
    if (typeof DouyinProductExtension !== 'undefined') {
        console.log('✅ 扩展类可用，创建新实例...');
        try {
            window.douyinExtension = new DouyinProductExtension();
            console.log('✅ 扩展实例创建成功');
            
            // 再次检查面板
            setTimeout(() => {
                panel = document.getElementById('douyin-extension-panel');
                if (panel) {
                    console.log('✅ 扩展面板创建成功');
                    panel.style.display = 'block';
                    panel.style.zIndex = '10000';
                } else {
                    console.log('❌ 扩展面板创建失败');
                }
            }, 1000);
            
        } catch (error) {
            console.error('❌ 扩展实例创建失败:', error);
        }
    } else {
        console.log('❌ 扩展类不可用');
        console.log('💡 请刷新页面或重新安装扩展');
    }
}

// 6. 如果面板存在但不可见，尝试显示
if (panel && panel.style.display === 'none') {
    console.log('🔧 面板存在但隐藏，尝试显示...');
    panel.style.display = 'block';
    panel.style.zIndex = '10000';
}

// 7. 检查必要的按钮
setTimeout(() => {
    const loadButton = document.getElementById('load-products');
    const createButton = document.getElementById('start-batch-create');
    const authButton = document.getElementById('setup-auth-params');
    
    console.log('🔘 加载商品按钮:', !!loadButton);
    console.log('🔘 批量创建按钮:', !!createButton);
    console.log('🔘 认证设置按钮:', !!authButton);
    
    if (loadButton && createButton && authButton) {
        console.log('✅ 所有按钮都存在，扩展应该可以正常工作');
    } else {
        console.log('❌ 部分按钮缺失，扩展可能有问题');
    }
}, 1500);

// 8. 提供手动操作指南
console.log('\n📋 手动操作指南:');
console.log('1. 如果看到扩展面板，点击"📂 自动加载所有商品数据"');
console.log('2. 然后点击"🔑 设置认证参数"');
console.log('3. 最后点击"🚀 开始批量创建"');
console.log('4. 如果面板不可见，请刷新页面');

// 9. 暴露修复函数
window.quickFix = function() {
    console.log('🔧 执行快速修复...');
    
    // 移除现有面板
    const existingPanel = document.getElementById('douyin-extension-panel');
    if (existingPanel) {
        existingPanel.remove();
    }
    
    // 重新创建扩展
    if (typeof DouyinProductExtension !== 'undefined') {
        window.douyinExtension = new DouyinProductExtension();
        console.log('✅ 扩展重新创建完成');
    } else {
        console.log('❌ 扩展类不可用，请刷新页面');
    }
};

console.log('💡 如果问题仍然存在，请在控制台运行: quickFix()');
